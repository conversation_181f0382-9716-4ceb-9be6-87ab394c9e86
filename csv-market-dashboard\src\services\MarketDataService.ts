// ============================================================================
// MARKET DATA SERVICE - WebSocket connection to Dhan API using CSV instruments
// ============================================================================

import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { Instrument, MarketData, SubscriptionRequest, MarketDataService as IMarketDataService } from '@/types';

// Exchange segment mapping for Dhan API
const EXCHANGE_SEGMENTS = {
  'NSE_EQ': 1,
  'NSE_FNO': 2,
  'BSE_EQ': 3,
  'BSE_FNO': 4,
  'MCX_COMM': 5,
  'IDX_I': 6,
} as const;

// Subscription types for Dhan API
const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
} as const;

export class MarketDataService extends EventEmitter implements IMarketDataService {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private accessToken: string;
  private clientId: string;
  private subscriptionType: string;
  private subscribedInstruments: Set<string> = new Set();
  private marketData: Map<string, MarketData> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 5000;

  constructor(accessToken?: string, clientId?: string, subscriptionType: string = 'quote') {
    super();
    this.accessToken = accessToken || process.env.ACCESS_TOKEN || '';
    this.clientId = clientId || process.env.CLIENT_ID || '';
    this.subscriptionType = subscriptionType;

    if (!this.accessToken || !this.clientId) {
      console.warn('⚠️ ACCESS_TOKEN and CLIENT_ID not provided - market data will be mocked');
    }
  }

  /**
   * Connect to Dhan WebSocket feed
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      console.log('Already connected to market feed');
      return;
    }

    try {
      this.reconnectAttempts++;
      console.log(`🔌 Connecting to Dhan market feed (attempt ${this.reconnectAttempts})`);

      // Check if we have valid credentials
      if (!this.accessToken || !this.clientId) {
        console.log('📊 No credentials provided - starting in mock mode');
        this.startMockDataMode();
        return;
      }

      // Construct WebSocket URL with authentication
      const wsUrl = `wss://api-feed.dhan.co?version=2&token=${encodeURIComponent(this.accessToken)}&clientId=${encodeURIComponent(this.clientId)}&authType=2`;

      this.ws = new WebSocket(wsUrl, {
        headers: {
          'User-Agent': 'CSV-Market-Dashboard/1.0',
        },
        handshakeTimeout: 10000,
      });

      this.setupWebSocketHandlers();

    } catch (error) {
      console.error('❌ Failed to connect to market feed:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('✅ Connected to Dhan live market feed');
      this.emit('connected');
    });

    this.ws.on('message', (data) => {
      this.handleMarketData(data);
    });

    this.ws.on('close', (code, reason) => {
      this.isConnected = false;
      console.log(`🔌 WebSocket connection closed: ${code} - ${reason}`);
      this.emit('disconnected');
      
      // Attempt reconnection
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => {
          this.connect();
        }, this.reconnectDelay);
      }
    });

    this.ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Handle incoming market data
   */
  private handleMarketData(data: WebSocket.Data): void {
    try {
      const message = JSON.parse(data.toString());
      
      // Process different message types
      if (message.type === 'quote' || message.type === 'ticker') {
        const marketData = this.parseMarketData(message);
        if (marketData) {
          this.marketData.set(marketData.securityId, marketData);
          this.emit('marketData', marketData);
        }
      }
    } catch (error) {
      console.error('❌ Error parsing market data:', error);
    }
  }

  /**
   * Parse raw market data message
   */
  private parseMarketData(message: any): MarketData | null {
    try {
      return {
        securityId: message.securityId?.toString() || '',
        symbol: message.symbol || '',
        exchange: message.exchange || '',
        ltp: parseFloat(message.ltp) || 0,
        change: parseFloat(message.change) || 0,
        changePercent: parseFloat(message.changePercent) || 0,
        volume: parseInt(message.volume) || 0,
        high: parseFloat(message.high) || 0,
        low: parseFloat(message.low) || 0,
        open: parseFloat(message.open) || 0,
        close: parseFloat(message.close) || 0,
        timestamp: Date.now(),
        bid: parseFloat(message.bid) || undefined,
        ask: parseFloat(message.ask) || undefined,
        bidQty: parseInt(message.bidQty) || undefined,
        askQty: parseInt(message.askQty) || undefined,
      };
    } catch (error) {
      console.error('❌ Error parsing market data message:', error);
      return null;
    }
  }

  /**
   * Subscribe to instruments
   */
  subscribe(instruments: Instrument[]): void {
    if (!this.isConnected) {
      console.warn('⚠️ Not connected to market feed - cannot subscribe');
      return;
    }

    const subscriptions: SubscriptionRequest[] = [];
    
    instruments.forEach(instrument => {
      if (!this.subscribedInstruments.has(instrument.securityId)) {
        subscriptions.push({
          exchangeSegment: instrument.exchangeCode,
          securityId: instrument.securityId,
        });
        this.subscribedInstruments.add(instrument.securityId);
      }
    });

    if (subscriptions.length > 0) {
      this.sendSubscriptionRequest(subscriptions, 'subscribe');
      console.log(`📡 Subscribed to ${subscriptions.length} instruments`);
    }
  }

  /**
   * Unsubscribe from instruments
   */
  unsubscribe(instruments: Instrument[]): void {
    if (!this.isConnected) {
      return;
    }

    const unsubscriptions: SubscriptionRequest[] = [];
    
    instruments.forEach(instrument => {
      if (this.subscribedInstruments.has(instrument.securityId)) {
        unsubscriptions.push({
          exchangeSegment: instrument.exchangeCode,
          securityId: instrument.securityId,
        });
        this.subscribedInstruments.delete(instrument.securityId);
        this.marketData.delete(instrument.securityId);
      }
    });

    if (unsubscriptions.length > 0) {
      this.sendSubscriptionRequest(unsubscriptions, 'unsubscribe');
      console.log(`📡 Unsubscribed from ${unsubscriptions.length} instruments`);
    }
  }

  /**
   * Send subscription request to WebSocket
   */
  private sendSubscriptionRequest(subscriptions: SubscriptionRequest[], action: 'subscribe' | 'unsubscribe'): void {
    if (!this.ws || !this.isConnected) return;

    const subscriptionTypeCode = SUBSCRIPTION_TYPES[this.subscriptionType as keyof typeof SUBSCRIPTION_TYPES] || SUBSCRIPTION_TYPES.quote;

    const message = {
      RequestCode: subscriptionTypeCode,
      InstrumentCount: subscriptions.length,
      InstrumentList: subscriptions,
    };

    try {
      this.ws.send(JSON.stringify(message));
    } catch (error) {
      console.error(`❌ Error sending ${action} request:`, error);
    }
  }

  /**
   * Get market data for a specific security
   */
  getMarketData(securityId: string): MarketData | null {
    return this.marketData.get(securityId) || null;
  }

  /**
   * Get all market data
   */
  getAllMarketData(): Map<string, MarketData> {
    return new Map(this.marketData);
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get subscription count
   */
  getSubscriptionCount(): number {
    return this.subscribedInstruments.size;
  }

  /**
   * Disconnect from market feed
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.subscribedInstruments.clear();
    this.marketData.clear();
    console.log('🔌 Disconnected from market feed');
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(): void {
    this.isConnected = false;
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      console.log(`🔄 Retrying connection in ${this.reconnectDelay / 1000} seconds...`);
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay);
    } else {
      console.log('❌ Max reconnection attempts reached - starting mock mode');
      this.startMockDataMode();
    }
  }

  /**
   * Start mock data mode for testing
   */
  private startMockDataMode(): void {
    this.isConnected = true;
    console.log('🎭 Starting mock market data mode');
    this.emit('connected');

    // Generate mock data every 2 seconds
    setInterval(() => {
      this.generateMockData();
    }, 2000);
  }

  /**
   * Generate mock market data
   */
  private generateMockData(): void {
    this.subscribedInstruments.forEach(securityId => {
      const existing = this.marketData.get(securityId);
      const basePrice = existing?.ltp || 100 + Math.random() * 1000;
      
      // Generate realistic price movement
      const changePercent = (Math.random() - 0.5) * 10; // ±5%
      const change = basePrice * (changePercent / 100);
      const newPrice = Math.max(0.01, basePrice + change);

      const mockData: MarketData = {
        securityId,
        symbol: `MOCK_${securityId}`,
        exchange: 'MOCK',
        ltp: parseFloat(newPrice.toFixed(2)),
        change: parseFloat(change.toFixed(2)),
        changePercent: parseFloat(changePercent.toFixed(2)),
        volume: Math.floor(Math.random() * 1000000),
        high: parseFloat((newPrice * (1 + Math.random() * 0.05)).toFixed(2)),
        low: parseFloat((newPrice * (1 - Math.random() * 0.05)).toFixed(2)),
        open: parseFloat((basePrice * (1 + (Math.random() - 0.5) * 0.02)).toFixed(2)),
        close: basePrice,
        timestamp: Date.now(),
      };

      this.marketData.set(securityId, mockData);
      this.emit('marketData', mockData);
    });
  }
}

// Export singleton instance
export const marketDataService = new MarketDataService();

// Export class for custom instances
export { MarketDataService };
