{"version": 3, "file": "websocket.d.ts", "sourceRoot": "", "sources": ["../../src/services/websocket.ts"], "names": [], "mappings": "AAEA,UAAU,gBAAgB;IACxB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,GAAG,CAAC;CACX;AAED,cAAM,gBAAgB;IACpB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAmB;IAC1C,OAAO,CAAC,EAAE,CAA0B;IACpC,OAAO,CAAC,iBAAiB,CAAK;IAC9B,OAAO,CAAC,iBAAiB,CAA+B;IACxD,OAAO,CAAC,YAAY,CAA0B;IAC9C,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,eAAe,CAAmD;IAE1E,OAAO;IAIP,MAAM,CAAC,WAAW,IAAI,gBAAgB;IAQhC,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsDzC,IAAI,CAAC,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAerC,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI;IAQ3D,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI;IAW7D,OAAO,CAAC,aAAa;IAiBrB,OAAO,CAAC,gBAAgB;IAwBxB,OAAO,CAAC,WAAW;IAMnB,OAAO,CAAC,cAAc;IAYtB,OAAO,CAAC,aAAa;IAQrB,OAAO,CAAC,mBAAmB;IAU3B,KAAK,IAAI,IAAI;IAYb,mBAAmB,IAAI,OAAO;CAG/B;AAED,eAAe,gBAAgB,CAAC"}