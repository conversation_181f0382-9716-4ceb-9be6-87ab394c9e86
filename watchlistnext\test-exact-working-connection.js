const { Pool } = require('pg');

console.log('🔍 Testing the EXACT working connection from our successful test...');

// This is the exact connection that worked in our npm run db:check test
const workingConfig = {
  connectionString: "***************************************************************/postgres",
  ssl: { rejectUnauthorized: false },
  connectionTimeoutMillis: 15000
};

async function testExactConnection() {
  try {
    console.log('⏳ Testing exact working connection...');
    
    const pool = new Pool(workingConfig);
    const client = await pool.connect();
    
    console.log('✅ Connected successfully!');
    
    // Test the company_list table
    const result = await client.query('SELECT COUNT(*) as count FROM company_list');
    console.log(`📊 Found ${result.rows[0].count} companies in company_list table`);
    
    // Test a sample query
    const sampleResult = await client.query('SELECT company_name, symbol, nse_security_id FROM company_list LIMIT 3');
    console.log('\n📋 Sample companies:');
    sampleResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.company_name} (${row.symbol}) - NSE: ${row.nse_security_id}`);
    });
    
    client.release();
    await pool.end();
    
    console.log('\n🎉 EXACT connection test PASSED!');
    console.log('This connection string definitely works.');
    
  } catch (error) {
    console.error('\n❌ EXACT connection test FAILED!');
    console.error(`Error: ${error.message}`);
  }
}

testExactConnection();
