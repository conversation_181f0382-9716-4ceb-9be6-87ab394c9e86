// ============================================================================
// CORE TYPE DEFINITIONS FOR DHAN WEBSOCKET SERVER
// ============================================================================

import { WebSocket as WS } from "ws";

// Market Data Types
export interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export interface IndexData {
  name: string;
  securityId: string;
  value: number;
  change: number;
  changePercent: number;
  lastUpdated: string;
  exchange: string;
}

// Legacy compatibility
export interface LegacyMarketData {
  symbol: string;
  exchange: string;
  lastPrice: number;
  change: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

// Instrument Types
export interface Instrument {
  securityId: number;
  ticker: string;
  exchange: string;
  exchangeCode: number;
  segment: string;
  lotUnits: number;
  name?: string;
}

export interface IndexInstrument {
  securityId: number;
  symbol: string;
  name: string;
  exchange: string;
  segment: string;
  lotUnits: number;
}

// WebSocket Types
export interface WebSocketMessage {
  type:
    | "subscribe"
    | "unsubscribe"
    | "connection"
    | "subscription"
    | "error"
    | "marketData"
    | "liveDataUpdate";
  symbols?: string[];
  status?: string;
  message?: string;
  data?: MarketData | LegacyMarketData;
  timestamp?: number;
}

export interface SubscriptionRequest {
  exchangeSegment: number;
  securityId: string;
}

export interface Client {
  id: string;
  ws: WS;
  subscriptions: Set<string>;
  lastActivity: number;
}

export interface Symbol {
  exchange: string;
  symbol: string;
}

// Database Types
export interface Company {
  id?: number;
  isin_no: string;
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_new_name?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface Watchlist {
  id: number;
  name: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface WatchlistItem {
  id: number;
  watchlist_id: number;
  security_id: string;
  exchange: string;
  ticker: string;
  added_at: Date;
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}

export interface IndicesResponse {
  connected: boolean;
  indices: MarketData[];
  totalIndices: number;
  activeIndices: number;
}

export interface HealthResponse {
  status: string;
  database: {
    connected: boolean;
    watchlists: number;
  };
  websocket: {
    connected: boolean;
    connections: number;
    instruments: number;
    bseInstruments?: number;
    bseLiveData?: number;
    bseDataPercentage?: string;
  };
  timestamp: number;
}

// Configuration Types
export interface ExchangeConfig {
  [key: string]: number;
}

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
}

export interface ServerConfig {
  port: number;
  host: string;
  cors: {
    origin: string[];
    credentials: boolean;
  };
}

// Error Types
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  isOperational?: boolean;
}

// Cache Types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheManager {
  get<T>(key: string): T | null;
  set<T>(key: string, value: T, ttl?: number): void;
  delete(key: string): boolean;
  clear(): void;
  size(): number;
}

// Service Types
export interface DataService {
  getMarketData(securityId: string): Promise<MarketData | null>;
  getIndices(limit?: number): Promise<MarketData[]>;
  getWatchlists(): Promise<Watchlist[]>;
  getWatchlistItems(watchlistId: number): Promise<WatchlistItem[]>;
  getCompanies(
    limit?: number,
    offset?: number,
    sector?: string,
    exchange?: string,
    search?: string
  ): Promise<{ companies: Company[]; total: number }>;
}

export interface WebSocketService {
  connect(): Promise<void>;
  disconnect(): void;
  subscribe(instruments: SubscriptionRequest[]): void;
  unsubscribe(instruments: SubscriptionRequest[]): void;
  isConnected(): boolean;
  getConnectionCount(): number;
}

// Component Props Types
export interface KeyIndicesProps {
  className?: string;
  exchange?: string;
  limit?: number;
  refreshInterval?: number;
}

export interface MarketOverviewProps {
  showIndices?: boolean;
  showStocks?: boolean;
  autoRefresh?: boolean;
}

// Utility Types
export type ExchangeSegment =
  | "NSE_EQ"
  | "NSE_FNO"
  | "BSE_EQ"
  | "IDX_I"
  | "MCX_COMM";
export type MarketStatus = "OPEN" | "CLOSED" | "PRE_OPEN" | "POST_CLOSE";
export type DataSource = "LIVE" | "MOCK" | "CACHED";

// Event Types
export interface MarketDataEvent {
  type: "MARKET_DATA_UPDATE";
  payload: MarketData;
}

export interface ConnectionEvent {
  type: "CONNECTION_STATUS";
  payload: {
    connected: boolean;
    timestamp: number;
  };
}

export type AppEvent = MarketDataEvent | ConnectionEvent;

// State Management Types
export interface AppState {
  marketData: Map<string, MarketData>;
  indices: MarketData[];
  watchlists: Watchlist[];
  connectionStatus: boolean;
  lastUpdate: number;
  error: string | null;
}

export interface AppAction {
  type: string;
  payload?: unknown;
}

// Validation Types
export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: "string" | "number" | "boolean" | "array" | "object";
  min?: number;
  max?: number;
  pattern?: RegExp;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Performance Monitoring Types
export interface PerformanceMetrics {
  requestCount: number;
  averageResponseTime: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
  timestamp: number;
}

// Logging Types
export interface LogEntry {
  level: "DEBUG" | "INFO" | "WARN" | "ERROR";
  message: string;
  timestamp: number;
  context?: Record<string, unknown>;
}

export interface Logger {
  debug(message: string, context?: Record<string, unknown>): void;
  info(message: string, context?: Record<string, unknown>): void;
  warn(message: string, context?: Record<string, unknown>): void;
  error(message: string, context?: Record<string, unknown>): void;
}
