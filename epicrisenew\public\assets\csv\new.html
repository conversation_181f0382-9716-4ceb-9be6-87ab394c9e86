<!DOCTYPE html>
<html>
  <head>
    <title>Candlestick Chart with ECharts</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
  </head>
  <body>
    <div id="chartContainer" style="width: 800px; height: 400px"></div>
    <script>
      var chartContainer = document.getElementById("chartContainer");
      var chart = echarts.init(chartContainer);

      var option = {
        title: {
          text: "Candlestick Chart",
        },
        xAxis: {
          type: "category",
          data: ["Day 1", "Day 2", "Day 3", "Day 4", "Day 5"], // Replace with your actual x-axis data
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            type: "candlestick",
            data: [
              [100, 200, 80, 150], // Replace with your actual candlestick data
              [150, 250, 120, 180],
              [180, 300, 160, 200],
              [120, 220, 90, 180],
              [160, 280, 140, 240],
            ],
          },
        ],
      };

      chart.setOption(option);
    </script>
  </body>
</html>
