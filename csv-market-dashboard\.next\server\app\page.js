/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CDashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CDashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CDashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEYXNoYm9hcmQlNUMlNUNjc3YtbWFya2V0LWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBNEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jc3YtbWFya2V0LWRhc2hib2FyZC8/ZmVlZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERhc2hib2FyZFxcXFxjc3YtbWFya2V0LWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDashboard%5C%5Ccsv-market-dashboard%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_InstrumentTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/InstrumentTable */ \"(ssr)/./src/components/InstrumentTable.tsx\");\n/* harmony import */ var _components_FilterPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FilterPanel */ \"(ssr)/./src/components/FilterPanel.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ConnectionStatus */ \"(ssr)/./src/components/ConnectionStatus.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Stats */ \"(ssr)/./src/components/Stats.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Dashboard() {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [instruments, setInstruments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [exchanges, setExchanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [instrumentTypes, setInstrumentTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Initialize socket connection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const serverUrl = \"http://localhost:8080\" || 0;\n        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(serverUrl, {\n            transports: [\n                \"websocket\",\n                \"polling\"\n            ],\n            timeout: 10000\n        });\n        socketInstance.on(\"connect\", ()=>{\n            console.log(\"✅ Connected to server\");\n            setConnected(true);\n            setError(null);\n        });\n        socketInstance.on(\"disconnect\", ()=>{\n            console.log(\"❌ Disconnected from server\");\n            setConnected(false);\n        });\n        socketInstance.on(\"initialData\", (data)=>{\n            console.log(\"\\uD83D\\uDCCA Received initial data:\", data);\n            setInstruments(data.instruments);\n            const marketDataMap = new Map();\n            data.marketData.forEach((md)=>{\n                marketDataMap.set(md.securityId, md);\n            });\n            setMarketData(marketDataMap);\n            setLoading(false);\n        });\n        socketInstance.on(\"marketData\", (data)=>{\n            setMarketData((prev)=>{\n                const newMap = new Map(prev);\n                newMap.set(data.securityId, data);\n                return newMap;\n            });\n        });\n        socketInstance.on(\"connectionStatus\", (data)=>{\n            setConnected(data.connected);\n        });\n        socketInstance.on(\"error\", (data)=>{\n            console.error(\"❌ Socket error:\", data);\n            setError(data.message);\n        });\n        setSocket(socketInstance);\n        return ()=>{\n            socketInstance.disconnect();\n        };\n    }, []);\n    // Load additional data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadMetadata = async ()=>{\n            try {\n                const serverUrl = \"http://localhost:8080\" || 0;\n                // Load exchanges\n                const exchangesResponse = await fetch(`${serverUrl}/api/exchanges`);\n                if (exchangesResponse.ok) {\n                    const exchangesData = await exchangesResponse.json();\n                    setExchanges(exchangesData.data);\n                }\n                // Load instrument types\n                const typesResponse = await fetch(`${serverUrl}/api/instrument-types`);\n                if (typesResponse.ok) {\n                    const typesData = await typesResponse.json();\n                    setInstrumentTypes(typesData.data);\n                }\n            } catch (error) {\n                console.error(\"❌ Error loading metadata:\", error);\n            }\n        };\n        loadMetadata();\n    }, []);\n    // Filter instruments based on current filter\n    const filteredInstruments = instruments.filter((instrument)=>{\n        if (filter.exchange && filter.exchange.length > 0 && !filter.exchange.includes(instrument.exchange)) {\n            return false;\n        }\n        if (filter.instrumentType && filter.instrumentType.length > 0 && !filter.instrumentType.includes(instrument.instrumentType)) {\n            return false;\n        }\n        if (filter.search) {\n            const searchTerm = filter.search.toLowerCase();\n            return instrument.symbol.toLowerCase().includes(searchTerm) || instrument.displayName.toLowerCase().includes(searchTerm) || instrument.isin && instrument.isin.toLowerCase().includes(searchTerm);\n        }\n        return true;\n    });\n    // Subscribe to instruments when filter changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (socket && filteredInstruments.length > 0) {\n            const securityIds = filteredInstruments.slice(0, 100).map((inst)=>inst.securityId); // Limit to 100 for performance\n            socket.emit(\"subscribe\", {\n                securityIds\n            });\n        }\n    }, [\n        socket,\n        filteredInstruments\n    ]);\n    const handleFilterChange = (newFilter)=>{\n        setFilter(newFilter);\n    };\n    const handleInstrumentSelect = (instrument)=>{\n        console.log(\"Selected instrument:\", instrument);\n    // You can add more functionality here, like showing detailed view\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-12 h-12 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading market data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-2xl shadow-lg p-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold gradient-text\",\n                                    children: \"CSV Market Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Real-time market data from CSV instruments\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            connected: connected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    \" \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                totalInstruments: instruments.length,\n                filteredInstruments: filteredInstruments.length,\n                marketDataCount: marketData.size,\n                connected: connected\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            filter: filter,\n                            onFilterChange: handleFilterChange,\n                            exchanges: exchanges,\n                            instrumentTypes: instrumentTypes,\n                            segments: [\n                                \"C\",\n                                \"F\",\n                                \"O\"\n                            ]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InstrumentTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            instruments: filteredInstruments.slice(0, 100),\n                            marketData: marketData,\n                            onInstrumentSelect: handleInstrumentSelect,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center text-sm text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"CSV Market Dashboard - Real-time data from \",\n                            instruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Last updated: \",\n                            new Date().toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst ConnectionStatus = ({ connected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-3 h-3 rounded-full ${connected ? \"bg-green-500 animate-pulse\" : \"bg-red-500\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${connected ? \"text-green-700\" : \"text-red-700\"}`,\n                        children: connected ? \"Connected\" : \"Disconnected\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${connected ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                children: connected ? \"LIVE\" : \"OFFLINE\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/FilterPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst FilterPanel = ({ filter, onFilterChange, exchanges, instrumentTypes, segments })=>{\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(filter.search || \"\");\n    const handleExchangeChange = (exchange, checked)=>{\n        const currentExchanges = filter.exchange || [];\n        const newExchanges = checked ? [\n            ...currentExchanges,\n            exchange\n        ] : currentExchanges.filter((e)=>e !== exchange);\n        onFilterChange({\n            ...filter,\n            exchange: newExchanges.length > 0 ? newExchanges : undefined\n        });\n    };\n    const handleInstrumentTypeChange = (type, checked)=>{\n        const currentTypes = filter.instrumentType || [];\n        const newTypes = checked ? [\n            ...currentTypes,\n            type\n        ] : currentTypes.filter((t)=>t !== type);\n        onFilterChange({\n            ...filter,\n            instrumentType: newTypes.length > 0 ? newTypes : undefined\n        });\n    };\n    const handleSegmentChange = (segment, checked)=>{\n        const currentSegments = filter.segment || [];\n        const newSegments = checked ? [\n            ...currentSegments,\n            segment\n        ] : currentSegments.filter((s)=>s !== segment);\n        onFilterChange({\n            ...filter,\n            segment: newSegments.length > 0 ? newSegments : undefined\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setSearchTerm(value);\n        onFilterChange({\n            ...filter,\n            search: value || undefined\n        });\n    };\n    const handleClearFilters = ()=>{\n        setSearchTerm(\"\");\n        onFilterChange({});\n    };\n    const isFilterActive = ()=>{\n        return !!(filter.exchange?.length || filter.instrumentType?.length || filter.segment?.length || filter.search || filter.isActive !== undefined || filter.hasExpiry !== undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filter-panel\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClearFilters,\n                        className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                        children: \"Clear All\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by symbol, name, or ISIN...\",\n                        value: searchTerm,\n                        onChange: (e)=>handleSearchChange(e.target.value),\n                        className: \"filter-input\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Exchanges\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: exchanges.map((exchange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.exchange?.includes(exchange) || false,\n                                        onChange: (e)=>handleExchangeChange(exchange, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, exchange, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Instrument Types\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 max-h-32 overflow-y-auto custom-scrollbar\",\n                        children: instrumentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.instrumentType?.includes(type) || false,\n                                        onChange: (e)=>handleInstrumentTypeChange(type, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: type\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, type, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Segments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.segment?.includes(segment) || false,\n                                        onChange: (e)=>handleSegmentChange(segment, e.target.checked),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: segment === \"C\" ? \"Cash (C)\" : segment === \"F\" ? \"Futures (F)\" : segment === \"O\" ? \"Options (O)\" : segment\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, segment, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Additional Filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.isActive === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                isActive: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Active Only\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: filter.hasExpiry === true,\n                                        onChange: (e)=>onFilterChange({\n                                                ...filter,\n                                                hasExpiry: e.target.checked ? true : undefined\n                                            }),\n                                        className: \"filter-checkbox\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: \"Has Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"filter-label\",\n                        children: \"Lot Size Range\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Min\",\n                                value: filter.minLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        minLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Max\",\n                                value: filter.maxLotSize || \"\",\n                                onChange: (e)=>onFilterChange({\n                                        ...filter,\n                                        maxLotSize: e.target.value ? parseInt(e.target.value) : undefined\n                                    }),\n                                className: \"filter-input text-sm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            isFilterActive() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                        children: \"Active Filters:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1 text-xs text-blue-700\",\n                        children: [\n                            filter.exchange?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Exchanges: \",\n                                    filter.exchange.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.instrumentType?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Types: \",\n                                    filter.instrumentType.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.segment?.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Segments: \",\n                                    filter.segment.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    'Search: \"',\n                                    filter.search,\n                                    '\"'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Active instruments only\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, undefined),\n                            filter.hasExpiry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"With expiry date\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, undefined),\n                            (filter.minLotSize || filter.maxLotSize) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Lot size: \",\n                                    filter.minLotSize || 0,\n                                    \" - \",\n                                    filter.maxLotSize || \"∞\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\FilterPanel.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InstrumentTable.tsx":
/*!********************************************!*\
  !*** ./src/components/InstrumentTable.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst InstrumentTable = ({ instruments, marketData, onInstrumentSelect, loading = false })=>{\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"symbol\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // Sort instruments\n    const sortedInstruments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            ...instruments\n        ].sort((a, b)=>{\n            const aValue = a[sortField];\n            const bValue = b[sortField];\n            if (aValue === bValue) return 0;\n            const comparison = aValue < bValue ? -1 : 1;\n            return sortDirection === \"asc\" ? comparison : -comparison;\n        });\n    }, [\n        instruments,\n        sortField,\n        sortDirection\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    const formatPrice = (price)=>{\n        if (price === undefined || price === 0) return \"-\";\n        return `₹${price.toFixed(2)}`;\n    };\n    const formatChange = (change, changePercent)=>{\n        if (change === undefined || changePercent === undefined) return \"-\";\n        const sign = change >= 0 ? \"+\" : \"\";\n        return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;\n    };\n    const getChangeColor = (change)=>{\n        if (change === undefined || change === 0) return \"text-gray-600\";\n        return change > 0 ? \"text-green-600\" : \"text-red-600\";\n    };\n    const formatVolume = (volume)=>{\n        if (volume === undefined || volume === 0) return \"-\";\n        if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;\n        if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;\n        if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;\n        return volume.toString();\n    };\n    const SortIcon = ({ field })=>{\n        if (sortField !== field) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-400\",\n                children: \"↕\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 63,\n                columnNumber: 14\n            }, undefined);\n        }\n        return sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↑\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 65,\n            columnNumber: 38\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: \"↓\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 65,\n            columnNumber: 81\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 mr-3\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading instruments...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (instruments.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"glass rounded-2xl shadow-lg p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-lg\",\n                        children: \"No instruments found\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-sm mt-2\",\n                        children: \"Try adjusting your filters\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"glass rounded-2xl shadow-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: \"Market Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mt-1\",\n                        children: [\n                            \"Showing \",\n                            sortedInstruments.length,\n                            \" instruments\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto custom-scrollbar\",\n                style: {\n                    maxHeight: \"600px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"market-table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"sticky top-0 bg-gray-50 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"symbol\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Symbol\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"symbol\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"displayName\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Name\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"displayName\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"exchange\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Exchange\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"instrumentType\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Type\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"instrumentType\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"LTP\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Change\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"cursor-pointer hover:bg-gray-100\",\n                                        onClick: ()=>handleSort(\"lotSize\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                \"Lot Size\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortIcon, {\n                                                    field: \"lotSize\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: sortedInstruments.map((instrument)=>{\n                                const data = marketData.get(instrument.securityId);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50 cursor-pointer transition-colors\",\n                                    onClick: ()=>onInstrumentSelect?.(instrument),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"font-medium text-blue-600\",\n                                            children: instrument.symbol\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"max-w-xs truncate\",\n                                            title: instrument.displayName,\n                                            children: instrument.displayName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: instrument.exchange\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                children: instrument.instrumentType\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right font-medium\",\n                                            children: formatPrice(data?.ltp)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: `text-right font-medium ${getChangeColor(data?.change)}`,\n                                            children: formatChange(data?.change, data?.changePercent)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: formatVolume(data?.volume)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"text-right\",\n                                            children: instrument.lotSize.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, instrument.securityId, true, {\n                                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            sortedInstruments.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-gray-50 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 text-center\",\n                    children: \"Showing first 100 instruments. Use filters to narrow down results.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\InstrumentTable.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstrumentTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JbnN0cnVtZW50VGFibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVpRDtBQUdqRCxNQUFNRyxrQkFBa0QsQ0FBQyxFQUN2REMsV0FBVyxFQUNYQyxVQUFVLEVBQ1ZDLGtCQUFrQixFQUNsQkMsVUFBVSxLQUFLLEVBQ2hCO0lBQ0MsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdSLCtDQUFRQSxDQUFtQjtJQUM3RCxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBaUI7SUFFbkUsbUJBQW1CO0lBQ25CLE1BQU1XLG9CQUFvQlYsOENBQU9BLENBQUM7UUFDaEMsT0FBTztlQUFJRTtTQUFZLENBQUNTLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUMvQixNQUFNQyxTQUFTRixDQUFDLENBQUNOLFVBQVU7WUFDM0IsTUFBTVMsU0FBU0YsQ0FBQyxDQUFDUCxVQUFVO1lBRTNCLElBQUlRLFdBQVdDLFFBQVEsT0FBTztZQUU5QixNQUFNQyxhQUFhRixTQUFTQyxTQUFTLENBQUMsSUFBSTtZQUMxQyxPQUFPUCxrQkFBa0IsUUFBUVEsYUFBYSxDQUFDQTtRQUNqRDtJQUNGLEdBQUc7UUFBQ2Q7UUFBYUk7UUFBV0U7S0FBYztJQUUxQyxNQUFNUyxhQUFhLENBQUNDO1FBQ2xCLElBQUlaLGNBQWNZLE9BQU87WUFDdkJULGlCQUFpQkQsa0JBQWtCLFFBQVEsU0FBUztRQUN0RCxPQUFPO1lBQ0xELGFBQWFXO1lBQ2JULGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTVUsY0FBYyxDQUFDQztRQUNuQixJQUFJQSxVQUFVQyxhQUFhRCxVQUFVLEdBQUcsT0FBTztRQUMvQyxPQUFPLENBQUMsQ0FBQyxFQUFFQSxNQUFNRSxPQUFPLENBQUMsR0FBRyxDQUFDO0lBQy9CO0lBRUEsTUFBTUMsZUFBZSxDQUFDQyxRQUE0QkM7UUFDaEQsSUFBSUQsV0FBV0gsYUFBYUksa0JBQWtCSixXQUFXLE9BQU87UUFDaEUsTUFBTUssT0FBT0YsVUFBVSxJQUFJLE1BQU07UUFDakMsT0FBTyxDQUFDLEVBQUVFLEtBQUssRUFBRUYsT0FBT0YsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFSSxLQUFLLEVBQUVELGNBQWNILE9BQU8sQ0FBQyxHQUFHLEVBQUUsQ0FBQztJQUM1RTtJQUVBLE1BQU1LLGlCQUFpQixDQUFDSDtRQUN0QixJQUFJQSxXQUFXSCxhQUFhRyxXQUFXLEdBQUcsT0FBTztRQUNqRCxPQUFPQSxTQUFTLElBQUksbUJBQW1CO0lBQ3pDO0lBRUEsTUFBTUksZUFBZSxDQUFDQztRQUNwQixJQUFJQSxXQUFXUixhQUFhUSxXQUFXLEdBQUcsT0FBTztRQUNqRCxJQUFJQSxVQUFVLFVBQVUsT0FBTyxDQUFDLEVBQUUsQ0FBQ0EsU0FBUyxRQUFPLEVBQUdQLE9BQU8sQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUNwRSxJQUFJTyxVQUFVLFFBQVEsT0FBTyxDQUFDLEVBQUUsQ0FBQ0EsU0FBUyxNQUFLLEVBQUdQLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUMvRCxJQUFJTyxVQUFVLE1BQU0sT0FBTyxDQUFDLEVBQUUsQ0FBQ0EsU0FBUyxJQUFHLEVBQUdQLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUMzRCxPQUFPTyxPQUFPQyxRQUFRO0lBQ3hCO0lBRUEsTUFBTUMsV0FBVyxDQUFDLEVBQUViLEtBQUssRUFBK0I7UUFDdEQsSUFBSVosY0FBY1ksT0FBTztZQUN2QixxQkFBTyw4REFBQ2M7Z0JBQUtDLFdBQVU7MEJBQWdCOzs7Ozs7UUFDekM7UUFDQSxPQUFPekIsa0JBQWtCLHNCQUFRLDhEQUFDd0I7WUFBS0MsV0FBVTtzQkFBZ0I7Ozs7O3NDQUFXLDhEQUFDRDtZQUFLQyxXQUFVO3NCQUFnQjs7Ozs7O0lBQzlHO0lBRUEsSUFBSTVCLFNBQVM7UUFDWCxxQkFDRSw4REFBQzZCO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUtDLFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUl4QztJQUVBLElBQUkvQixZQUFZaUMsTUFBTSxLQUFLLEdBQUc7UUFDNUIscUJBQ0UsOERBQUNEO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0c7d0JBQUVILFdBQVU7a0NBQXdCOzs7Ozs7a0NBQ3JDLDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSWxEO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlELFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNJO3dCQUFHSixXQUFVO2tDQUFrQzs7Ozs7O2tDQUNoRCw4REFBQ0c7d0JBQUVILFdBQVU7OzRCQUE2Qjs0QkFDL0J2QixrQkFBa0J5QixNQUFNOzRCQUFDOzs7Ozs7Ozs7Ozs7OzBCQUl0Qyw4REFBQ0Q7Z0JBQUlELFdBQVU7Z0JBQW1DSyxPQUFPO29CQUFFQyxXQUFXO2dCQUFROzBCQUM1RSw0RUFBQ0M7b0JBQU1QLFdBQVU7O3NDQUNmLDhEQUFDUTs0QkFBTVIsV0FBVTtzQ0FDZiw0RUFBQ1M7O2tEQUNDLDhEQUFDQzt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNM0IsV0FBVztrREFFMUIsNEVBQUNpQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNiLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdwQiw4REFBQ3lCO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0zQixXQUFXO2tEQUUxQiw0RUFBQ2lCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU2IsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDeUI7d0NBQ0NWLFdBQVU7d0NBQ1ZXLFNBQVMsSUFBTTNCLFdBQVc7a0RBRTFCLDRFQUFDaUI7NENBQUlELFdBQVU7O2dEQUFvQzs4REFFakQsOERBQUNGO29EQUFTYixPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHcEIsOERBQUN5Qjt3Q0FDQ1YsV0FBVTt3Q0FDVlcsU0FBUyxJQUFNM0IsV0FBVztrREFFMUIsNEVBQUNpQjs0Q0FBSUQsV0FBVTs7Z0RBQW9DOzhEQUVqRCw4REFBQ0Y7b0RBQVNiLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdwQiw4REFBQ3lCO3dDQUFHVixXQUFVO2tEQUFhOzs7Ozs7a0RBQzNCLDhEQUFDVTt3Q0FBR1YsV0FBVTtrREFBYTs7Ozs7O2tEQUMzQiw4REFBQ1U7d0NBQUdWLFdBQVU7a0RBQWE7Ozs7OztrREFDM0IsOERBQUNVO3dDQUNDVixXQUFVO3dDQUNWVyxTQUFTLElBQU0zQixXQUFXO2tEQUUxQiw0RUFBQ2lCOzRDQUFJRCxXQUFVOztnREFBb0M7OERBRWpELDhEQUFDRjtvREFBU2IsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLeEIsOERBQUMyQjtzQ0FDRW5DLGtCQUFrQm9DLEdBQUcsQ0FBQyxDQUFDQztnQ0FDdEIsTUFBTUMsT0FBTzdDLFdBQVc4QyxHQUFHLENBQUNGLFdBQVdHLFVBQVU7Z0NBQ2pELHFCQUNFLDhEQUFDUjtvQ0FFQ1QsV0FBVTtvQ0FDVlcsU0FBUyxJQUFNeEMscUJBQXFCMkM7O3NEQUVwQyw4REFBQ0k7NENBQUdsQixXQUFVO3NEQUNYYyxXQUFXSyxNQUFNOzs7Ozs7c0RBRXBCLDhEQUFDRDs0Q0FBR2xCLFdBQVU7NENBQW9Cb0IsT0FBT04sV0FBV08sV0FBVztzREFDNURQLFdBQVdPLFdBQVc7Ozs7OztzREFFekIsOERBQUNIO3NEQUNDLDRFQUFDbkI7Z0RBQUtDLFdBQVU7MERBQ2JjLFdBQVdRLFFBQVE7Ozs7Ozs7Ozs7O3NEQUd4Qiw4REFBQ0o7c0RBQ0MsNEVBQUNuQjtnREFBS0MsV0FBVTswREFDYmMsV0FBV1MsY0FBYzs7Ozs7Ozs7Ozs7c0RBRzlCLDhEQUFDTDs0Q0FBR2xCLFdBQVU7c0RBQ1hkLFlBQVk2QixNQUFNUzs7Ozs7O3NEQUVyQiw4REFBQ047NENBQUdsQixXQUFXLENBQUMsdUJBQXVCLEVBQUVOLGVBQWVxQixNQUFNeEIsUUFBUSxDQUFDO3NEQUNwRUQsYUFBYXlCLE1BQU14QixRQUFRd0IsTUFBTXZCOzs7Ozs7c0RBRXBDLDhEQUFDMEI7NENBQUdsQixXQUFVO3NEQUNYTCxhQUFhb0IsTUFBTW5COzs7Ozs7c0RBRXRCLDhEQUFDc0I7NENBQUdsQixXQUFVO3NEQUNYYyxXQUFXVyxPQUFPLENBQUNDLGNBQWM7Ozs7Ozs7bUNBOUIvQlosV0FBV0csVUFBVTs7Ozs7NEJBa0NoQzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLTHhDLGtCQUFrQnlCLE1BQU0sR0FBRyxxQkFDMUIsOERBQUNEO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDRztvQkFBRUgsV0FBVTs4QkFBb0M7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzNEO0FBRUEsaUVBQWVoQyxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvY29tcG9uZW50cy9JbnN0cnVtZW50VGFibGUudHN4PzJhZmMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEluc3RydW1lbnQsIE1hcmtldERhdGEsIEluc3RydW1lbnRUYWJsZVByb3BzIH0gZnJvbSAnQC90eXBlcyc7XG5cbmNvbnN0IEluc3RydW1lbnRUYWJsZTogUmVhY3QuRkM8SW5zdHJ1bWVudFRhYmxlUHJvcHM+ID0gKHtcbiAgaW5zdHJ1bWVudHMsXG4gIG1hcmtldERhdGEsXG4gIG9uSW5zdHJ1bWVudFNlbGVjdCxcbiAgbG9hZGluZyA9IGZhbHNlLFxufSkgPT4ge1xuICBjb25zdCBbc29ydEZpZWxkLCBzZXRTb3J0RmllbGRdID0gdXNlU3RhdGU8a2V5b2YgSW5zdHJ1bWVudD4oJ3N5bWJvbCcpO1xuICBjb25zdCBbc29ydERpcmVjdGlvbiwgc2V0U29ydERpcmVjdGlvbl0gPSB1c2VTdGF0ZTwnYXNjJyB8ICdkZXNjJz4oJ2FzYycpO1xuXG4gIC8vIFNvcnQgaW5zdHJ1bWVudHNcbiAgY29uc3Qgc29ydGVkSW5zdHJ1bWVudHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICByZXR1cm4gWy4uLmluc3RydW1lbnRzXS5zb3J0KChhLCBiKSA9PiB7XG4gICAgICBjb25zdCBhVmFsdWUgPSBhW3NvcnRGaWVsZF07XG4gICAgICBjb25zdCBiVmFsdWUgPSBiW3NvcnRGaWVsZF07XG4gICAgICBcbiAgICAgIGlmIChhVmFsdWUgPT09IGJWYWx1ZSkgcmV0dXJuIDA7XG4gICAgICBcbiAgICAgIGNvbnN0IGNvbXBhcmlzb24gPSBhVmFsdWUgPCBiVmFsdWUgPyAtMSA6IDE7XG4gICAgICByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyBjb21wYXJpc29uIDogLWNvbXBhcmlzb247XG4gICAgfSk7XG4gIH0sIFtpbnN0cnVtZW50cywgc29ydEZpZWxkLCBzb3J0RGlyZWN0aW9uXSk7XG5cbiAgY29uc3QgaGFuZGxlU29ydCA9IChmaWVsZDoga2V5b2YgSW5zdHJ1bWVudCkgPT4ge1xuICAgIGlmIChzb3J0RmllbGQgPT09IGZpZWxkKSB7XG4gICAgICBzZXRTb3J0RGlyZWN0aW9uKHNvcnREaXJlY3Rpb24gPT09ICdhc2MnID8gJ2Rlc2MnIDogJ2FzYycpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRTb3J0RmllbGQoZmllbGQpO1xuICAgICAgc2V0U29ydERpcmVjdGlvbignYXNjJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFByaWNlID0gKHByaWNlOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmIChwcmljZSA9PT0gdW5kZWZpbmVkIHx8IHByaWNlID09PSAwKSByZXR1cm4gJy0nO1xuICAgIHJldHVybiBg4oK5JHtwcmljZS50b0ZpeGVkKDIpfWA7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0Q2hhbmdlID0gKGNoYW5nZTogbnVtYmVyIHwgdW5kZWZpbmVkLCBjaGFuZ2VQZXJjZW50OiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmIChjaGFuZ2UgPT09IHVuZGVmaW5lZCB8fCBjaGFuZ2VQZXJjZW50ID09PSB1bmRlZmluZWQpIHJldHVybiAnLSc7XG4gICAgY29uc3Qgc2lnbiA9IGNoYW5nZSA+PSAwID8gJysnIDogJyc7XG4gICAgcmV0dXJuIGAke3NpZ259JHtjaGFuZ2UudG9GaXhlZCgyKX0gKCR7c2lnbn0ke2NoYW5nZVBlcmNlbnQudG9GaXhlZCgyKX0lKWA7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2hhbmdlQ29sb3IgPSAoY2hhbmdlOiBudW1iZXIgfCB1bmRlZmluZWQpOiBzdHJpbmcgPT4ge1xuICAgIGlmIChjaGFuZ2UgPT09IHVuZGVmaW5lZCB8fCBjaGFuZ2UgPT09IDApIHJldHVybiAndGV4dC1ncmF5LTYwMCc7XG4gICAgcmV0dXJuIGNoYW5nZSA+IDAgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCc7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0Vm9sdW1lID0gKHZvbHVtZTogbnVtYmVyIHwgdW5kZWZpbmVkKTogc3RyaW5nID0+IHtcbiAgICBpZiAodm9sdW1lID09PSB1bmRlZmluZWQgfHwgdm9sdW1lID09PSAwKSByZXR1cm4gJy0nO1xuICAgIGlmICh2b2x1bWUgPj0gMTAwMDAwMDApIHJldHVybiBgJHsodm9sdW1lIC8gMTAwMDAwMDApLnRvRml4ZWQoMSl9Q3JgO1xuICAgIGlmICh2b2x1bWUgPj0gMTAwMDAwKSByZXR1cm4gYCR7KHZvbHVtZSAvIDEwMDAwMCkudG9GaXhlZCgxKX1MYDtcbiAgICBpZiAodm9sdW1lID49IDEwMDApIHJldHVybiBgJHsodm9sdW1lIC8gMTAwMCkudG9GaXhlZCgxKX1LYDtcbiAgICByZXR1cm4gdm9sdW1lLnRvU3RyaW5nKCk7XG4gIH07XG5cbiAgY29uc3QgU29ydEljb24gPSAoeyBmaWVsZCB9OiB7IGZpZWxkOiBrZXlvZiBJbnN0cnVtZW50IH0pID0+IHtcbiAgICBpZiAoc29ydEZpZWxkICE9PSBmaWVsZCkge1xuICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj7ihpU8L3NwYW4+O1xuICAgIH1cbiAgICByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gJ2FzYycgPyA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+4oaRPC9zcGFuPiA6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj7ihpM8L3NwYW4+O1xuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3Mgcm91bmRlZC0yeGwgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtNjRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwaW5uZXIgdy04IGgtOCBtci0zXCI+PC9kaXY+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgaW5zdHJ1bWVudHMuLi48L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChpbnN0cnVtZW50cy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJnbGFzcyByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgcC02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtbGdcIj5ObyBpbnN0cnVtZW50cyBmb3VuZDwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc20gbXQtMlwiPlRyeSBhZGp1c3RpbmcgeW91ciBmaWx0ZXJzPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xhc3Mgcm91bmRlZC0yeGwgc2hhZG93LWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+TWFya2V0IEluc3RydW1lbnRzPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtIG10LTFcIj5cbiAgICAgICAgICBTaG93aW5nIHtzb3J0ZWRJbnN0cnVtZW50cy5sZW5ndGh9IGluc3RydW1lbnRzXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0byBjdXN0b20tc2Nyb2xsYmFyXCIgc3R5bGU9e3sgbWF4SGVpZ2h0OiAnNjAwcHgnIH19PlxuICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWFya2V0LXRhYmxlXCI+XG4gICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCBiZy1ncmF5LTUwIHotMTBcIj5cbiAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgPHRoIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KCdzeW1ib2wnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICBTeW1ib2xcbiAgICAgICAgICAgICAgICAgIDxTb3J0SWNvbiBmaWVsZD1cInN5bWJvbFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDx0aCBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydCgnZGlzcGxheU5hbWUnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICBOYW1lXG4gICAgICAgICAgICAgICAgICA8U29ydEljb24gZmllbGQ9XCJkaXNwbGF5TmFtZVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDx0aCBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydCgnZXhjaGFuZ2UnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICBFeGNoYW5nZVxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwiZXhjaGFuZ2VcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICA8dGggXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ2luc3RydW1lbnRUeXBlJyl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgVHlwZVxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwiaW5zdHJ1bWVudFR5cGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPkxUUDwvdGg+XG4gICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+Q2hhbmdlPC90aD5cbiAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5Wb2x1bWU8L3RoPlxuICAgICAgICAgICAgICA8dGggXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS0xMDBcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoJ2xvdFNpemUnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICBMb3QgU2l6ZVxuICAgICAgICAgICAgICAgICAgPFNvcnRJY29uIGZpZWxkPVwibG90U2l6ZVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICA8L3RyPlxuICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgPHRib2R5PlxuICAgICAgICAgICAge3NvcnRlZEluc3RydW1lbnRzLm1hcCgoaW5zdHJ1bWVudCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBkYXRhID0gbWFya2V0RGF0YS5nZXQoaW5zdHJ1bWVudC5zZWN1cml0eUlkKTtcbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8dHJcbiAgICAgICAgICAgICAgICAgIGtleT17aW5zdHJ1bWVudC5zZWN1cml0eUlkfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6YmctZ3JheS01MCBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkluc3RydW1lbnRTZWxlY3Q/LihpbnN0cnVtZW50KX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5zeW1ib2x9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cIm1heC13LXhzIHRydW5jYXRlXCIgdGl0bGU9e2luc3RydW1lbnQuZGlzcGxheU5hbWV9PlxuICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5kaXNwbGF5TmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQ+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7aW5zdHJ1bWVudC5leGNoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50Lmluc3RydW1lbnRUeXBlfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtcmlnaHQgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKGRhdGE/Lmx0cCl9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT17YHRleHQtcmlnaHQgZm9udC1tZWRpdW0gJHtnZXRDaGFuZ2VDb2xvcihkYXRhPy5jaGFuZ2UpfWB9PlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0Q2hhbmdlKGRhdGE/LmNoYW5nZSwgZGF0YT8uY2hhbmdlUGVyY2VudCl9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFZvbHVtZShkYXRhPy52b2x1bWUpfVxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgIHtpbnN0cnVtZW50LmxvdFNpemUudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgIDwvdGFibGU+XG4gICAgICA8L2Rpdj5cblxuICAgICAge3NvcnRlZEluc3RydW1lbnRzLmxlbmd0aCA+IDEwMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICBTaG93aW5nIGZpcnN0IDEwMCBpbnN0cnVtZW50cy4gVXNlIGZpbHRlcnMgdG8gbmFycm93IGRvd24gcmVzdWx0cy5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEluc3RydW1lbnRUYWJsZTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlTWVtbyIsIkluc3RydW1lbnRUYWJsZSIsImluc3RydW1lbnRzIiwibWFya2V0RGF0YSIsIm9uSW5zdHJ1bWVudFNlbGVjdCIsImxvYWRpbmciLCJzb3J0RmllbGQiLCJzZXRTb3J0RmllbGQiLCJzb3J0RGlyZWN0aW9uIiwic2V0U29ydERpcmVjdGlvbiIsInNvcnRlZEluc3RydW1lbnRzIiwic29ydCIsImEiLCJiIiwiYVZhbHVlIiwiYlZhbHVlIiwiY29tcGFyaXNvbiIsImhhbmRsZVNvcnQiLCJmaWVsZCIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJ1bmRlZmluZWQiLCJ0b0ZpeGVkIiwiZm9ybWF0Q2hhbmdlIiwiY2hhbmdlIiwiY2hhbmdlUGVyY2VudCIsInNpZ24iLCJnZXRDaGFuZ2VDb2xvciIsImZvcm1hdFZvbHVtZSIsInZvbHVtZSIsInRvU3RyaW5nIiwiU29ydEljb24iLCJzcGFuIiwiY2xhc3NOYW1lIiwiZGl2IiwibGVuZ3RoIiwicCIsImgyIiwic3R5bGUiLCJtYXhIZWlnaHQiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsIm9uQ2xpY2siLCJ0Ym9keSIsIm1hcCIsImluc3RydW1lbnQiLCJkYXRhIiwiZ2V0Iiwic2VjdXJpdHlJZCIsInRkIiwic3ltYm9sIiwidGl0bGUiLCJkaXNwbGF5TmFtZSIsImV4Y2hhbmdlIiwiaW5zdHJ1bWVudFR5cGUiLCJsdHAiLCJsb3RTaXplIiwidG9Mb2NhbGVTdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InstrumentTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Stats = ({ totalInstruments, filteredInstruments, marketDataCount, connected })=>{\n    const stats = [\n        {\n            label: \"Total Instruments\",\n            value: totalInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDCCA\",\n            color: \"bg-blue-500\"\n        },\n        {\n            label: \"Filtered Results\",\n            value: filteredInstruments.toLocaleString(),\n            icon: \"\\uD83D\\uDD0D\",\n            color: \"bg-purple-500\"\n        },\n        {\n            label: \"Live Data\",\n            value: marketDataCount.toLocaleString(),\n            icon: \"\\uD83D\\uDCC8\",\n            color: connected ? \"bg-green-500\" : \"bg-gray-500\"\n        },\n        {\n            label: \"Connection\",\n            value: connected ? \"Active\" : \"Inactive\",\n            icon: connected ? \"\\uD83D\\uDFE2\" : \"\\uD83D\\uDD34\",\n            color: connected ? \"bg-green-500\" : \"bg-red-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"glass rounded-xl shadow-lg p-4 card-hover\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-600\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-gray-900 mt-1\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl\",\n                                children: stat.icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mt-3 h-1 rounded-full ${stat.color}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, index, true, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Stats);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Stats.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"085ee3861088\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3N2LW1hcmtldC1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2QzNzYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwODVlZTM4NjEwODhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"CSV Market Dashboard\",\n    description: \"Real-time market data dashboard using CSV instrument subscription\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Dashboard\\\\csv-market-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQzlCLDRFQUFDVTtnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2Nzdi1tYXJrZXQtZGFzaGJvYXJkLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQ1NWIE1hcmtldCBEYXNoYm9hcmQnLFxuICBkZXNjcmlwdGlvbjogJ1JlYWwtdGltZSBtYXJrZXQgZGF0YSBkYXNoYm9hcmQgdXNpbmcgQ1NWIGluc3RydW1lbnQgc3Vic2NyaXB0aW9uJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTUwIHZpYS1ibHVlLTUwLzMwIHRvLWluZGlnby01MC8yMFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dashboard\csv-market-dashboard\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Dashboard\csv-market-dashboard\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CDashboard%5Ccsv-market-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDashboard%5Ccsv-market-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();