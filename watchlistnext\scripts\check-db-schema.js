const { Pool } = require('pg');
require('dotenv').config();

console.log('🔍 Checking database schema and connection...');

// Use the working connection
const pool = new Pool({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  connectionTimeoutMillis: 15000
});

async function checkDatabaseSchema() {
  try {
    console.log('🔗 Connecting to Supabase database...');
    
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Check if company_list table exists
    const tableInfoQuery = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'company_list'
      ORDER BY ordinal_position;
    `;
    
    const tableInfo = await client.query(tableInfoQuery);
    
    if (tableInfo.rows.length === 0) {
      console.log('❌ Company_list table not found. Checking available tables...');
      
      const tablesQuery = `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
      `;
      
      const tables = await client.query(tablesQuery);
      console.log('📋 Available tables:');
      tables.rows.forEach(row => {
        console.log(`  - ${row.table_name}`);
      });
      
    } else {
      console.log('✅ Company_list table found with columns:');
      tableInfo.rows.forEach(row => {
        console.log(`  - ${row.column_name} (${row.data_type}) ${row.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Get record count
      const countResult = await client.query('SELECT COUNT(*) as count FROM company_list');
      console.log(`\n📊 Total records: ${countResult.rows[0].count}`);
      
      // Get sample data
      console.log('\n📋 Sample data (first 3 rows):');
      const sampleQuery = 'SELECT company_name, symbol, sector_name, nse_security_id, bse_security_id FROM company_list LIMIT 3';
      const sampleData = await client.query(sampleQuery);
      
      sampleData.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.company_name} (${row.symbol})`);
        console.log(`   Sector: ${row.sector_name}`);
        console.log(`   NSE ID: ${row.nse_security_id || 'N/A'}, BSE ID: ${row.bse_security_id || 'N/A'}`);
      });
      
      // Count records with exchange data
      const nseCount = await client.query('SELECT COUNT(*) as count FROM company_list WHERE nse_security_id IS NOT NULL');
      const bseCount = await client.query('SELECT COUNT(*) as count FROM company_list WHERE bse_security_id IS NOT NULL');
      const dualCount = await client.query('SELECT COUNT(*) as count FROM company_list WHERE nse_security_id IS NOT NULL AND bse_security_id IS NOT NULL');
      
      console.log('\n📈 Exchange Distribution:');
      console.log(`  - Companies with NSE ID: ${nseCount.rows[0].count}`);
      console.log(`  - Companies with BSE ID: ${bseCount.rows[0].count}`);
      console.log(`  - Dual listed companies: ${dualCount.rows[0].count}`);
      
      // Get sector distribution
      const sectorQuery = `
        SELECT sector_name, COUNT(*) as count 
        FROM company_list 
        WHERE sector_name IS NOT NULL 
        GROUP BY sector_name 
        ORDER BY count DESC 
        LIMIT 10
      `;
      
      const sectorData = await client.query(sectorQuery);
      console.log('\n🏢 Top 10 Sectors:');
      sectorData.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.sector_name}: ${row.count} companies`);
      });
      
      // Check indexes
      const indexQuery = `
        SELECT indexname, indexdef 
        FROM pg_indexes 
        WHERE tablename = 'company_list'
        ORDER BY indexname;
      `;
      
      const indexes = await client.query(indexQuery);
      console.log('\n🔍 Database Indexes:');
      if (indexes.rows.length > 0) {
        indexes.rows.forEach(row => {
          console.log(`  - ${row.indexname}`);
        });
      } else {
        console.log('  - No custom indexes found (only primary key)');
      }
    }
    
    client.release();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 DATABASE SCHEMA CHECK COMPLETED');
    console.log('='.repeat(60));
    console.log('✅ Connection: Working');
    console.log('✅ Table: company_list exists');
    console.log('✅ Data: Populated with company records');
    console.log('✅ Ready for application use');
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check .env file has POSTGRES_DATABASE_URL');
    console.log('2. Verify Supabase project is active');
    console.log('3. Check network connection');
    console.log('4. Run: npm run db:migrate (if table missing)');
    console.log('5. Run: npm run db:import (if data missing)');
  } finally {
    await pool.end();
  }
}

checkDatabaseSchema();
