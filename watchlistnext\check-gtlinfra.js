const { Pool } = require('pg');

const pool = new Pool({
  connectionString: '***************************************************************/postgres',
  ssl: { rejectUnauthorized: false }
});

async function checkSymbol() {
  try {
    const client = await pool.connect();
    
    console.log('🔍 Searching for GTLINFRA in database...');
    
    // Check what GTLINFRA looks like in the database
    const result = await client.query(`
      SELECT symbol, company_name, nse_security_id, bse_security_id, sector_name, industry_new_name 
      FROM company_list 
      WHERE symbol ILIKE '%GTLINFRA%' OR company_name ILIKE '%GTL%'
      LIMIT 10
    `);
    
    console.log(`Found ${result.rows.length} results for GTLINFRA/GTL:`);
    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. Symbol: ${row.symbol}`);
      console.log(`   Company: ${row.company_name}`);
      console.log(`   NSE ID: ${row.nse_security_id}`);
      console.log(`   BSE ID: ${row.bse_security_id}`);
      console.log(`   Sector: ${row.sector_name}`);
      console.log('   ---');
    });
    
    // Also check exact match
    const exactResult = await client.query(`
      SELECT symbol, company_name, nse_security_id, bse_security_id, sector_name, industry_new_name 
      FROM company_list 
      WHERE symbol = 'GTLINFRA'
      LIMIT 1
    `);
    
    console.log(`\n🎯 Exact match for 'GTLINFRA':`);
    if (exactResult.rows.length > 0) {
      const row = exactResult.rows[0];
      console.log(`✅ Found: ${row.symbol} - ${row.company_name}`);
      console.log(`   NSE ID: ${row.nse_security_id}`);
      console.log(`   BSE ID: ${row.bse_security_id}`);
    } else {
      console.log(`❌ No exact match found for 'GTLINFRA'`);
    }
    
    client.release();
    await pool.end();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkSymbol();
