import { Logger } from "@/types";
export declare class LoggerService implements Logger {
    private logLevel;
    private enableConsole;
    private enableFile;
    private logBuffer;
    private maxBufferSize;
    private flushInterval;
    constructor(logLevel?: "DEBUG" | "INFO" | "WARN" | "ERROR", enableConsole?: boolean, enableFile?: boolean, maxBufferSize?: number);
    /**
     * Log debug message
     */
    debug(message: string, context?: Record<string, any>): void;
    /**
     * Log info message
     */
    info(message: string, context?: Record<string, any>): void;
    /**
     * Log warning message
     */
    warn(message: string, context?: Record<string, any>): void;
    /**
     * Log error message
     */
    error(message: string, context?: Record<string, any>): void;
    /**
     * Core internal logging method
     */
    private logInternal;
    /**
     * Log to console with colors
     */
    private logToConsole;
    /**
     * Add log entry to buffer
     */
    private addToBuffer;
    /**
     * Check if message should be logged based on level
     */
    private shouldLog;
    /**
     * Start periodic buffer flushing
     */
    private startFlushInterval;
    /**
     * Flush log buffer to file
     */
    private flushBuffer;
    /**
     * Set log level
     */
    setLogLevel(level: "DEBUG" | "INFO" | "WARN" | "ERROR"): void;
    /**
     * Get current log level
     */
    getLogLevel(): "DEBUG" | "INFO" | "WARN" | "ERROR";
    /**
     * Enable/disable console logging
     */
    setConsoleLogging(enabled: boolean): void;
    /**
     * Enable/disable file logging
     */
    setFileLogging(enabled: boolean): void;
    /**
     * Get buffer statistics
     */
    getBufferStats(): {
        size: number;
        maxSize: number;
        utilization: number;
    };
    /**
     * Force flush buffer
     */
    flush(): void;
    /**
     * Cleanup resources
     */
    destroy(): void;
    /**
     * Create child logger with context
     */
    child(context: Record<string, any>): Logger;
    /**
     * Log performance metrics
     */
    performance(operation: string, duration: number, context?: Record<string, any>): void;
    /**
     * Log with custom level (public interface)
     */
    log(level: "DEBUG" | "INFO" | "WARN" | "ERROR", message: string, context?: Record<string, any>): void;
}
export declare const logger: LoggerService;
export default LoggerService;
//# sourceMappingURL=LoggerService.d.ts.map