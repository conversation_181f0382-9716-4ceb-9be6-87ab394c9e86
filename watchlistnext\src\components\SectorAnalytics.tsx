"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Zap,
} from "lucide-react";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  companyName?: string;
  sector?: string;
  industry?: string;
}

interface SectorData {
  sector: string;
  stocks: StockData[];
  totalStocks: number;
  gainers: number;
  losers: number;
  avgChange: number;
  totalVolume: number;
}

interface SectorAnalyticsProps {
  sectorData: SectorData[];
  selectedExchange: string;
}

export default function SectorAnalytics({
  sectorData,
  selectedExchange,
}: SectorAnalyticsProps) {
  // Check if market data is available
  const hasMarketData =
    sectorData.length > 0 &&
    sectorData.some((sector) => sector.totalStocks > 0);
  const isMarketClosed = selectedExchange === "BSE" && !hasMarketData;

  // Calculate market insights
  const totalStocks = sectorData.reduce(
    (sum, sector) => sum + sector.totalStocks,
    0
  );
  const totalGainers = sectorData.reduce(
    (sum, sector) => sum + sector.gainers,
    0
  );
  const totalLosers = sectorData.reduce(
    (sum, sector) => sum + sector.losers,
    0
  );
  const totalVolume = sectorData.reduce(
    (sum, sector) => sum + sector.totalVolume,
    0
  );

  // Market sentiment
  const bullishSentiment =
    totalStocks > 0 ? (totalGainers / totalStocks) * 100 : 0;
  const bearishSentiment =
    totalStocks > 0 ? (totalLosers / totalStocks) * 100 : 0;

  // Top performing sectors
  const topGainerSectors = [...sectorData]
    .filter((s) => s.avgChange > 0)
    .sort((a, b) => b.avgChange - a.avgChange)
    .slice(0, 5);

  const topLoserSectors = [...sectorData]
    .filter((s) => s.avgChange < 0)
    .sort((a, b) => a.avgChange - b.avgChange)
    .slice(0, 5);

  // Most active sectors by volume
  const mostActiveSectors = [...sectorData]
    .sort((a, b) => b.totalVolume - a.totalVolume)
    .slice(0, 5);

  // Top individual stocks across all sectors
  const allStocks = sectorData.flatMap((sector) => sector.stocks);
  const topGainerStocks = [...allStocks]
    .filter((s) => s.changePercent > 0)
    .sort((a, b) => b.changePercent - a.changePercent)
    .slice(0, 10);

  const topLoserStocks = [...allStocks]
    .filter((s) => s.changePercent < 0)
    .sort((a, b) => a.changePercent - b.changePercent)
    .slice(0, 10);

  const mostActiveStocks = [...allStocks]
    .sort((a, b) => b.volume - a.volume)
    .slice(0, 10);

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  // const formatChange = (change: number, changePercent: number) => {
  //   const sign = change >= 0 ? "+" : "";
  //   return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  // };

  const formatVolume = (volume: number) => {
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  return (
    <div className="space-y-6">
      {/* Market Closed Notice */}
      {isMarketClosed && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 rounded-full bg-orange-500 animate-pulse"></div>
              <div>
                <p className="font-medium text-orange-800">BSE Market Closed</p>
                <p className="text-sm text-orange-600">
                  BSE trading hours: 9:15 AM - 3:30 PM IST. Real-time data will
                  be available during market hours.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Market Sentiment Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Market Sentiment
                </p>
                <p
                  className={`text-2xl font-bold ${bullishSentiment > bearishSentiment ? "text-green-600" : "text-red-600"}`}
                >
                  {bullishSentiment > bearishSentiment ? "Bullish" : "Bearish"}
                </p>
                <p className="text-xs text-muted-foreground">
                  {bullishSentiment.toFixed(1)}% Bullish
                </p>
              </div>
              <Activity
                className={`h-8 w-8 ${bullishSentiment > bearishSentiment ? "text-green-500" : "text-red-500"}`}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Volume</p>
                <p className="text-2xl font-bold">
                  {formatVolume(totalVolume)}
                </p>
                <p className="text-xs text-muted-foreground">
                  Across {totalStocks} stocks
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Sectors</p>
                <p className="text-2xl font-bold">{sectorData.length}</p>
                <p className="text-xs text-muted-foreground">
                  {sectorData.filter((s) => s.avgChange > 0).length} gaining
                </p>
              </div>
              <PieChart className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">G/L Ratio</p>
                <p className="text-2xl font-bold">
                  {totalLosers > 0
                    ? (totalGainers / totalLosers).toFixed(2)
                    : "∞"}
                </p>
                <p className="text-xs text-muted-foreground">
                  {totalGainers}G : {totalLosers}L
                </p>
              </div>
              <Target className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Sectors */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Top Gaining Sectors
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {topGainerSectors.map((sector, index) => (
              <div
                key={sector.sector}
                className="flex items-center justify-between p-3 rounded-lg bg-green-50"
              >
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-xs font-bold text-green-700">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{sector.sector}</p>
                    <p className="text-xs text-muted-foreground">
                      {sector.totalStocks} stocks
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className="bg-green-100 text-green-800">
                    +{sector.avgChange.toFixed(2)}%
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {sector.gainers}↑ {sector.losers}↓
                  </p>
                </div>
              </div>
            ))}
            {topGainerSectors.length === 0 && (
              <p className="text-center text-muted-foreground py-4">
                No gaining sectors
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5 text-red-500" />
              Top Declining Sectors
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {topLoserSectors.map((sector, index) => (
              <div
                key={sector.sector}
                className="flex items-center justify-between p-3 rounded-lg bg-red-50"
              >
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center text-xs font-bold text-red-700">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{sector.sector}</p>
                    <p className="text-xs text-muted-foreground">
                      {sector.totalStocks} stocks
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="destructive">
                    {sector.avgChange.toFixed(2)}%
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {sector.gainers}↑ {sector.losers}↓
                  </p>
                </div>
              </div>
            ))}
            {topLoserSectors.length === 0 && (
              <p className="text-center text-muted-foreground py-4">
                No declining sectors
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Most Active Sectors by Volume */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Most Active Sectors by Volume
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mostActiveSectors.map((sector, index) => (
              <div
                key={sector.sector}
                className="p-4 rounded-lg border bg-blue-50"
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{sector.sector}</h4>
                  <Badge variant="outline">#{index + 1}</Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Volume:{" "}
                    <span className="font-medium">
                      {formatVolume(sector.totalVolume)}
                    </span>
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Stocks:{" "}
                    <span className="font-medium">{sector.totalStocks}</span>
                  </p>
                  <p
                    className={`text-sm font-medium ${sector.avgChange >= 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    Avg: {sector.avgChange >= 0 ? "+" : ""}
                    {sector.avgChange.toFixed(2)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Individual Stocks */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <TrendingUp className="h-5 w-5" />
              Top Gainers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {topGainerStocks.slice(0, 5).map((stock) => (
              <div
                key={stock.securityId}
                className="flex items-center justify-between p-2 rounded bg-green-50"
              >
                <div className="flex-1">
                  <p className="font-medium text-sm">{stock.ticker}</p>
                  <p className="text-xs text-muted-foreground truncate">
                    {stock.sector}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">
                    {formatPrice(stock.ltp)}
                  </p>
                  <p className="text-xs text-green-600">
                    +{stock.changePercent.toFixed(2)}%
                  </p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <TrendingDown className="h-5 w-5" />
              Top Losers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {topLoserStocks.slice(0, 5).map((stock) => (
              <div
                key={stock.securityId}
                className="flex items-center justify-between p-2 rounded bg-red-50"
              >
                <div className="flex-1">
                  <p className="font-medium text-sm">{stock.ticker}</p>
                  <p className="text-xs text-muted-foreground truncate">
                    {stock.sector}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">
                    {formatPrice(stock.ltp)}
                  </p>
                  <p className="text-xs text-red-600">
                    {stock.changePercent.toFixed(2)}%
                  </p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-600">
              <BarChart3 className="h-5 w-5" />
              Most Active
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {mostActiveStocks.slice(0, 5).map((stock) => (
              <div
                key={stock.securityId}
                className="flex items-center justify-between p-2 rounded bg-blue-50"
              >
                <div className="flex-1">
                  <p className="font-medium text-sm">{stock.ticker}</p>
                  <p className="text-xs text-muted-foreground truncate">
                    {stock.sector}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">
                    {formatPrice(stock.ltp)}
                  </p>
                  <p className="text-xs text-blue-600">
                    {formatVolume(stock.volume)}
                  </p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
