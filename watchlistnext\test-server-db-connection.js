const { Pool } = require("pg");
require("dotenv").config();

console.log("🔍 Testing server database connection...");

// Test the exact same configuration the server uses
function parsePostgresConfig() {
  const postgresUrl = process.env.POSTGRES_DATABASE_URL;

  console.log("📋 Environment variables:");
  console.log(`POSTGRES_DATABASE_URL: ${postgresUrl ? "Set" : "Not set"}`);
  console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? "Set" : "Not set"}`);
  console.log(`DIRECT_URL: ${process.env.DIRECT_URL ? "Set" : "Not set"}`);

  if (postgresUrl) {
    console.log(
      `🔗 Using POSTGRES_DATABASE_URL: ${postgresUrl.substring(0, 50)}...`
    );
    // Parse the PostgreSQL URL
    const url = new URL(postgresUrl);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 5432,
      database: url.pathname.slice(1), // Remove leading slash
      user: url.username,
      password: url.password,
      ssl: false, // Disable SSL to avoid SASL issues
    };
  }

  // Fallback to individual environment variables
  console.log("⚠️ Using fallback environment variables");
  return {
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "dhan_websocket",
    user: process.env.DB_USER || "postgres",
    password: process.env.DB_PASSWORD || "password",
    ssl: false,
  };
}

async function testConnection() {
  try {
    const config = parsePostgresConfig();

    console.log("\n🔧 Connection configuration:");
    console.log(`Host: ${config.host}`);
    console.log(`Port: ${config.port}`);
    console.log(`Database: ${config.database}`);
    console.log(`User: ${config.user}`);
    console.log(`Password: ***${config.password?.slice(-4) || "none"}`);
    console.log(`SSL: ${config.ssl ? "Enabled" : "Disabled"}`);

    const pool = new Pool({
      ...config,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 15000,
      maxUses: 7500,
    });

    console.log("\n⏳ Testing connection...");
    const client = await pool.connect();

    console.log("✅ Connected successfully!");

    // Test the exact query the server uses
    console.log("\n🧪 Testing company_list table query...");
    const testQuery = `
      SELECT 
        c.symbol as ticker,
        c.nse_security_id as security_id,
        'NSE' as exchange,
        1 as exchange_code
      FROM company_list c 
      WHERE c.symbol IS NOT NULL AND c.nse_security_id IS NOT NULL 
      AND c.symbol != '-' AND c.nse_security_id != '-'
      LIMIT 5
    `;

    const result = await client.query(testQuery);
    console.log(`✅ Query successful! Found ${result.rows.length} companies:`);

    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.ticker} (NSE ID: ${row.security_id})`);
    });

    client.release();
    await pool.end();

    console.log("\n🎉 Server database connection test PASSED!");
    console.log("The server should be able to connect to the database.");
  } catch (error) {
    console.error("\n❌ Server database connection test FAILED!");
    console.error(`Error: ${error.message}`);

    if (error.message.includes("SASL")) {
      console.log("\n💡 SASL error suggests authentication issue");
      console.log("Check if the password in POSTGRES_DATABASE_URL is correct");
    }
  }
}

testConnection();
