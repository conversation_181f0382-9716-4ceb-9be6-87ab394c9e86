"use strict";
// ============================================================================
// CACHE SERVICE - High-Performance In-Memory Caching
// ============================================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheKeys = exports.cacheService = exports.CacheService = void 0;
class CacheService {
    constructor(maxSize = 10000, defaultTTL = 300000) {
        // 5 minutes default
        this.cache = new Map();
        this.maxSize = maxSize;
        this.defaultTTL = defaultTTL;
        this.cleanupInterval = null;
        this.startCleanupInterval();
    }
    /**
     * Get value from cache
     */
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if entry has expired
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    /**
     * Set value in cache
     */
    set(key, value, ttl) {
        // Enforce cache size limit
        if (this.cache.size >= this.maxSize) {
            this.evictOldest();
        }
        const entry = {
            data: value,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL,
        };
        this.cache.set(key, entry);
    }
    /**
     * Delete specific key from cache
     */
    delete(key) {
        return this.cache.delete(key);
    }
    /**
     * Clear all cache entries
     */
    clear() {
        this.cache.clear();
    }
    /**
     * Get current cache size
     */
    size() {
        return this.cache.size;
    }
    /**
     * Get cache statistics
     */
    getStats() {
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            hitRate: this.calculateHitRate(),
            memoryUsage: this.estimateMemoryUsage(),
        };
    }
    /**
     * Check if cache entry has expired
     */
    isExpired(entry) {
        return Date.now() - entry.timestamp > entry.ttl;
    }
    /**
     * Evict oldest entries when cache is full
     */
    evictOldest() {
        let oldestKey = null;
        let oldestTimestamp = Date.now();
        for (const [key, entry] of Array.from(this.cache.entries())) {
            if (entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
    /**
     * Start periodic cleanup of expired entries
     */
    startCleanupInterval() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupExpired();
        }, 60000); // Cleanup every minute
    }
    /**
     * Remove all expired entries
     */
    cleanupExpired() {
        const now = Date.now();
        const keysToDelete = [];
        for (const [key, entry] of Array.from(this.cache.entries())) {
            if (now - entry.timestamp > entry.ttl) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach((key) => this.cache.delete(key));
    }
    /**
     * Calculate cache hit rate (simplified)
     */
    calculateHitRate() {
        // This is a simplified implementation
        // In a real scenario, you'd track hits and misses
        return 0.85; // Placeholder
    }
    /**
     * Estimate memory usage (simplified)
     */
    estimateMemoryUsage() {
        // Rough estimation: each entry ~1KB
        return this.cache.size * 1024;
    }
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.cache.clear();
    }
    /**
     * Get all keys matching a pattern
     */
    getKeys(pattern) {
        const keys = Array.from(this.cache.keys());
        if (!pattern) {
            return keys;
        }
        return keys.filter((key) => pattern.test(key));
    }
    /**
     * Check if key exists in cache
     */
    has(key) {
        const entry = this.cache.get(key);
        return entry !== undefined && !this.isExpired(entry);
    }
    /**
     * Get multiple values at once
     */
    getMultiple(keys) {
        const result = new Map();
        keys.forEach((key) => {
            result.set(key, this.get(key));
        });
        return result;
    }
    /**
     * Set multiple values at once
     */
    setMultiple(entries, ttl) {
        entries.forEach((value, key) => {
            this.set(key, value, ttl);
        });
    }
    /**
     * Increment a numeric value in cache
     */
    increment(key, delta = 1) {
        const current = this.get(key) || 0;
        const newValue = current + delta;
        this.set(key, newValue);
        return newValue;
    }
    /**
     * Decrement a numeric value in cache
     */
    decrement(key, delta = 1) {
        return this.increment(key, -delta);
    }
}
exports.CacheService = CacheService;
// Singleton instance
exports.cacheService = new CacheService();
// Cache key generators
exports.CacheKeys = {
    marketData: (securityId) => `market:${securityId}`,
    indices: (limit) => `indices:${limit}`,
    watchlist: (id) => `watchlist:${id}`,
    watchlistItems: (id) => `watchlist:${id}:items`,
    health: () => "health",
    instruments: () => "instruments",
    subscriptions: () => "subscriptions",
};
exports.default = CacheService;
//# sourceMappingURL=CacheService.js.map