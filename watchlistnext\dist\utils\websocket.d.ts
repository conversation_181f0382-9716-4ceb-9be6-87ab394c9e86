import { Socket } from "socket.io-client";
interface WebSocketOptions {
    onConnect?: () => void;
    onDisconnect?: (reason: string) => void;
    onError?: (error: Error) => void;
    onReconnect?: (attemptNumber: number) => void;
    onMarketData?: (data: any) => void;
    onMarketDataBatch?: (data: any[]) => void;
}
export declare const createWebSocketConnection: (options?: WebSocketOptions) => Socket;
export declare const removeWebSocketListeners: (onMarketData?: (...args: any[]) => void, onMarketDataBatch?: (...args: any[]) => void) => void;
export declare const cleanupWebSocketConnection: () => void;
export declare const getWebSocketStatus: () => {
    connected: boolean;
    clients: number;
};
export {};
//# sourceMappingURL=websocket.d.ts.map