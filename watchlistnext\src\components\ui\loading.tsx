import React from "react";
import { cn } from "@/lib/utils";

interface LoadingProps {
  variant?: "spinner" | "dots" | "pulse" | "skeleton";
  size?: "sm" | "default" | "lg";
  className?: string;
  text?: string;
}

const Loading: React.FC<LoadingProps> = ({
  variant = "spinner",
  size = "default",
  className,
  text
}) => {
  const sizes = {
    sm: "w-4 h-4",
    default: "w-6 h-6",
    lg: "w-8 h-8"
  };

  const renderSpinner = () => (
    <div className={cn("animate-spin rounded-full border-2 border-gray-300 border-t-primary-600", sizes[size])} />
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      <div className={cn("bg-primary-600 rounded-full animate-bounce", sizes[size])} style={{ animationDelay: "0ms" }} />
      <div className={cn("bg-primary-600 rounded-full animate-bounce", sizes[size])} style={{ animationDelay: "150ms" }} />
      <div className={cn("bg-primary-600 rounded-full animate-bounce", sizes[size])} style={{ animationDelay: "300ms" }} />
    </div>
  );

  const renderPulse = () => (
    <div className={cn("bg-primary-600 rounded-full animate-pulse-soft", sizes[size])} />
  );

  const renderSkeleton = () => (
    <div className="space-y-3">
      <div className="h-4 bg-gray-200 rounded animate-pulse" />
      <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
      <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2" />
    </div>
  );

  const renderVariant = () => {
    switch (variant) {
      case "dots":
        return renderDots();
      case "pulse":
        return renderPulse();
      case "skeleton":
        return renderSkeleton();
      default:
        return renderSpinner();
    }
  };

  return (
    <div className={cn("flex flex-col items-center justify-center space-y-2", className)}>
      {renderVariant()}
      {text && (
        <p className="text-sm text-gray-600 animate-pulse-soft">{text}</p>
      )}
    </div>
  );
};

// Skeleton components for specific use cases
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("p-6 space-y-4", className)}>
    <div className="h-6 bg-gray-200 rounded animate-pulse w-1/3" />
    <div className="space-y-2">
      <div className="h-4 bg-gray-200 rounded animate-pulse" />
      <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6" />
    </div>
    <div className="flex space-x-2">
      <div className="h-8 bg-gray-200 rounded animate-pulse w-20" />
      <div className="h-8 bg-gray-200 rounded animate-pulse w-16" />
    </div>
  </div>
);

export const SkeletonTable: React.FC<{ rows?: number; className?: string }> = ({ 
  rows = 5, 
  className 
}) => (
  <div className={cn("space-y-3", className)}>
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex space-x-4">
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3" />
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/6" />
        <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
      </div>
    ))}
  </div>
);

export const SkeletonChart: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("space-y-4", className)}>
    <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4" />
    <div className="h-64 bg-gray-200 rounded animate-pulse" />
    <div className="flex justify-between">
      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
      <div className="h-4 bg-gray-200 rounded animate-pulse w-16" />
    </div>
  </div>
);

export default Loading;
