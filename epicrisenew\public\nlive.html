<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Epic Rise Performance</title>
    <style>
      /* General Styles */
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f2f4f8;
        margin: 0;
        padding: 0;
        color: #333;
      }

      /* Header Section */
      header {
        background-color: #2c3e50;
        color: #ecf0f1;
        padding: 30px 20px;
        text-align: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid #34495e;
      }

      .header-content {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
      }

      header img {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        margin-right: 20px;
      }

      header h1 {
        font-size: 36px;
        margin: 0;
        font-weight: bold;
        letter-spacing: 2px;
      }

      header span {
        display: block;
        margin-top: 10px;
        font-size: 18px;
        color: #bdc3c7;
      }

      .live {
        font-weight: bold;
        color: #ff4757;
        font-size: 20px;
        margin-left: 10px;
        animation: blink 1s infinite alternate;
      }

      .live-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        background-color: #ff4757;
        border-radius: 50%;
        margin-left: 8px;
        animation: blink-dot 1s infinite alternate;
        animation-delay: 0.5s;
      }

      /* Blinking Text Animation */
      @keyframes blink {
        0% {
          color: #ff4757;
        }
        50% {
          color: #2ed573;
        }
        100% {
          color: #ff4757;
        }
      }

      @keyframes blink-dot {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      /* Container */
      .container {
        width: 90%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 30px;
        background-color: #ffffff;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
      }

      /* Table Styles */
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 30px;
      }

      th,
      td {
        padding: 14px 20px;
        text-align: center;
        font-size: 16px;
        border: 1px solid #ddd;
      }

      th {
        background-color: #2c3e50;
        color: white;
        font-size: 18px;
        font-weight: bold;
      }

      td {
        background-color: #f7f8f9;
      }

      tfoot td {
        font-weight: bold;
        background-color: #ecf0f1;
      }

      .positive {
        color: #27ae60;
      }

      .negative {
        color: #c0392b;
      }

      /* Footer Section */
      footer {
        background-color: #2c3e50;
        color: white;
        text-align: center;
        padding: 20px;
        font-size: 14px;
        margin-top: 40px;
        border-top: 2px solid #34495e;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        header h1 {
          font-size: 28px;
        }

        table {
          font-size: 14px;
        }

        th,
        td {
          padding: 10px;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-content">
        <img src="./assets/img/epicrise.jpg" alt="Epic Rise Logo" />
        <h1>Epic Rise Performance</h1>
      </div>
      <span id="current-date"></span>
      <span class="live">LIVE <span class="live-dot"></span></span>
    </header>

    <div class="container">
      <table id="positions-table">
        <thead>
          <tr>
            <th>SR NO</th>
            <th>Script</th>
            <th>Buy Price</th>
            <th>Sell Price</th>
            <th>Net Quantity</th>
            <th>Last Trading Price (LTP)</th>
            <th>Absolute Profit/Loss</th>
            <th>Achievement</th>
          </tr>
        </thead>
        <tbody>
          <!-- Dynamic rows will be inserted here -->
        </tbody>
        <tfoot>
          <tr>
            <td colspan="6" style="text-align: right; font-size: 18px">
              Total
            </td>
            <td
              id="total-absolute"
              class="positive"
              style="font-size: 18px"
            ></td>
            <td></td>
          </tr>
        </tfoot>
      </table>
    </div>

    <footer>&copy; 2025 Epic Rise Performance | All Rights Reserved</footer>

    <script>
      // Function to fetch and display data
      async function fetchPositions() {
        try {
          const response = await fetch("/getPositionCli");
          const data = await response.json();
          console.log("API Response:", data); // Debugging log

          if (data.status) {
            const tableBody = document.querySelector("#positions-table tbody");
            const totalAbsoluteCell = document.getElementById("total-absolute");
            tableBody.innerHTML = ""; // Clear existing rows

            let totalAbsoluteProfitLoss = 0;

            data.data.forEach((position, index) => {
              const row = document.createElement("tr");

              const absoluteProfitLoss = parseFloat(position.pnl).toFixed(2);
              totalAbsoluteProfitLoss += parseFloat(absoluteProfitLoss);

              // Default achievement is "Open" in green
              let achievement = '<span style="color: green;">Open</span>';

              // Update achievement if netqty is 0
              if (parseFloat(position.netqty) === 0) {
                if (absoluteProfitLoss < 0) {
                  achievement = '<span style="color: red;">Loss Booked</span>';
                } else if (absoluteProfitLoss > 0) {
                  achievement =
                    '<span style="color: green;">Profit Booked</span>';
                }
              }

              row.innerHTML = `
                <td>${index + 1}</td>
                <td>${position.symbolname}</td>
                <td>${parseFloat(position.buyavgprice).toFixed(2)}</td>
                <td>${parseFloat(position.sellavgprice).toFixed(2)}</td>
                <td>${position.netqty}</td>
                <td>${parseFloat(position.ltp).toFixed(2)}</td>
                <td class="${
                  position.pnl >= 0 ? "positive" : "negative"
                }">${absoluteProfitLoss}</td>
                <td>${achievement}</td>
              `;

              tableBody.appendChild(row);
            });

            // Update total absolute profit/loss in the footer
            totalAbsoluteCell.textContent = totalAbsoluteProfitLoss.toFixed(2);
            totalAbsoluteCell.className =
              totalAbsoluteProfitLoss >= 0 ? "positive" : "negative";
          } else {
            console.error("Failed to fetch data:", data.message);
          }
        } catch (error) {
          console.error("Error fetching positions:", error);
        }
      }

      const currentDate = new Date();
      const dateElement = document.getElementById("current-date");
      dateElement.textContent =
        currentDate.toDateString() + " " + currentDate.toLocaleTimeString();

      // Call the function to fetch and display data on page load
      fetchPositions();
    </script>
  </body>
</html>
