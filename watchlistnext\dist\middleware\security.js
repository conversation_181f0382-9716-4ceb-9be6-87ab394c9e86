"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityErrorHandler = exports.sanitizeRequest = exports.validateApiKey = exports.validateFileUpload = exports.validateRequest = exports.securityHeaders = exports.corsOptions = exports.rateLimiter = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const config_1 = require("../config");
const error_1 = require("./error");
// Rate limiter middleware
exports.rateLimiter = (0, express_rate_limit_1.default)({
    windowMs: config_1.appConfig.security.rateLimitWindowMs,
    max: config_1.appConfig.security.rateLimitMaxRequests,
    message: "Too many requests from this IP, please try again later",
    handler: (req, res) => {
        throw (0, error_1.rateLimitError)("Rate limit exceeded");
    },
});
// CORS configuration
exports.corsOptions = (0, cors_1.default)({
    origin: config_1.appConfig.security.allowedOrigins,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
    maxAge: 86400, // 24 hours
});
// Security headers middleware
exports.securityHeaders = (0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "wss:", "https:"],
            fontSrc: ["'self'", "https:", "data:"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: true,
    crossOriginOpenerPolicy: true,
    crossOriginResourcePolicy: { policy: "same-site" },
    dnsPrefetchControl: { allow: false },
    frameguard: { action: "deny" },
    hidePoweredBy: true,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
    },
    ieNoOpen: true,
    noSniff: true,
    originAgentCluster: true,
    permittedCrossDomainPolicies: { permittedPolicies: "none" },
    referrerPolicy: { policy: "strict-origin-when-cross-origin" },
    xssFilter: true,
});
// Request validation middleware
const validateRequest = (schema) => {
    return (req, res, next) => {
        try {
            schema.parse({
                body: req.body,
                query: req.query,
                params: req.params,
            });
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validateRequest = validateRequest;
// File upload validation middleware
const validateFileUpload = (req, res, next) => {
    if (!req.file) {
        return next();
    }
    const file = req.file;
    const maxSize = config_1.appConfig.upload.maxFileSize;
    const allowedExtensions = config_1.appConfig.upload.allowedExtensions;
    // Check file size
    if (file.size > maxSize) {
        return res.status(400).json({
            success: false,
            error: "File too large",
            message: `File size must be less than ${maxSize / 1024 / 1024}MB`,
        });
    }
    // Check file extension
    const ext = file.originalname.split(".").pop()?.toLowerCase();
    if (!ext || !allowedExtensions.includes(`.${ext}`)) {
        return res.status(400).json({
            success: false,
            error: "Invalid file type",
            message: `Only ${allowedExtensions.join(", ")} files are allowed`,
        });
    }
    next();
};
exports.validateFileUpload = validateFileUpload;
// API key validation middleware
const validateApiKey = (req, res, next) => {
    const apiKey = req.headers["x-api-key"];
    if (!apiKey || apiKey !== config_1.appConfig.api.accessToken) {
        res.status(401).json({
            success: false,
            error: "Invalid API key",
            message: "Please provide a valid API key",
        });
        return;
    }
    next();
};
exports.validateApiKey = validateApiKey;
// Request sanitization middleware
const sanitizeRequest = (req, res, next) => {
    // Sanitize query parameters
    if (req.query) {
        Object.keys(req.query).forEach((key) => {
            if (typeof req.query[key] === "string") {
                req.query[key] = sanitizeString(req.query[key]);
            }
        });
    }
    // Sanitize request body
    if (req.body) {
        Object.keys(req.body).forEach((key) => {
            if (typeof req.body[key] === "string") {
                req.body[key] = sanitizeString(req.body[key]);
            }
        });
    }
    next();
};
exports.sanitizeRequest = sanitizeRequest;
// String sanitization helper
function sanitizeString(str) {
    return str
        .replace(/[<>]/g, "") // Remove < and >
        .replace(/javascript:/gi, "") // Remove javascript: protocol
        .replace(/on\w+=/gi, "") // Remove on* attributes
        .trim();
}
// Error handling for security middleware
const securityErrorHandler = (err, req, res, next) => {
    if (err.name === "RateLimitExceeded") {
        res.status(429).json({
            success: false,
            error: "Rate limit exceeded",
            message: "Too many requests, please try again later",
        });
        return;
    }
    if (err.name === "CorsError") {
        res.status(403).json({
            success: false,
            error: "CORS error",
            message: "Cross-origin request not allowed",
        });
        return;
    }
    next(err);
};
exports.securityErrorHandler = securityErrorHandler;
//# sourceMappingURL=security.js.map