const { Client } = require('pg');

async function checkSymbols() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    
    // Get sample symbols from database
    const result = await client.query('SELECT symbol, company_name, sector_name FROM company_list WHERE symbol IS NOT NULL LIMIT 10');
    console.log('Sample symbols in database:');
    result.rows.forEach(row => {
      console.log(`Symbol: ${row.symbol}, Company: ${row.company_name}, Sector: ${row.sector_name}`);
    });
    
    console.log('\n--- Checking websocket symbols ---');
    
    // Check if any symbols match the websocket format
    const wsSymbols = ['20MICRONS', '21STCENMGM', '3MINDIA', 'RELIANCE', 'INFY', 'TCS'];
    for (const symbol of wsSymbols) {
      const match = await client.query('SELECT symbol, company_name, sector_name FROM company_list WHERE symbol ILIKE $1', [symbol]);
      if (match.rows.length > 0) {
        console.log(`✅ Found match for ${symbol}:`, match.rows[0]);
      } else {
        console.log(`❌ No match found for ${symbol}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

checkSymbols();
