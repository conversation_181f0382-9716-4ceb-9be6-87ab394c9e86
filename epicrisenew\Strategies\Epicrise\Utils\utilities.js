const axios = require("axios");

function createFakeResponse(resolve) {
  return {
    status: () => ({
      json: resolve,
      send: resolve,
    }),
    json: resolve,
    send: resolve,
    end: resolve,
  };
}

// Function to escape special characters for MarkdownV2
function escapeMarkdownV2(text) {
  // Escape special characters except emojis and formatting characters
  return text.replace(/[_*[\]()~`>#+=|{}.!-]/g, "\\$&");
}

async function sendMessageToTelegram(botToken, channelId, messageText) {
  try {
    if (!botToken || !channelId) {
      console.error("Missing Telegram credentials");
      return { ok: false, error: "Missing Telegram credentials" };
    }

    // Escape all special characters except emojis
    const escapedMessage = messageText.replace(
      /[_*[\]()~`>#+=|{}.!-]/g,
      "\\$&"
    );

    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    const requestData = {
      chat_id: channelId,
      text: escapedMessage,
      parse_mode: "MarkdownV2",
    };

    // console.log("Sending request to Telegram API:", {
    //   url,
    //   chat_id: channelId,
    //   messageLength: escapedMessage.length,
    // });

    const response = await axios.post(url, requestData, {
      timeout: 10000, // 10 second timeout
      headers: {
        "Content-Type": "application/json",
      },
    });

    console.log("Telegram API response:", {
      status: response.status,
      ok: response.data?.ok,
      messageId: response.data?.result?.message_id,
    });

    return { ok: true, data: response.data };
  } catch (error) {
    console.error("Error sending message to Telegram:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });

    if (error.code === "ECONNABORTED") {
      return {
        ok: false,
        error: "Request timed out after 10 seconds",
        details: error.message,
      };
    }

    return {
      ok: false,
      error: error.response?.data?.description || error.message,
      status: error.response?.status,
    };
  }
}

function EangelparseMessageText(messageText) {
  if (!messageText || typeof messageText !== "string") {
    return null;
  }

  const cleanMessage = messageText.trim().replace(/\s+/g, " ");
  const regex =
    /ER (Buy|Sell) (\w+) at (\d+(\.\d+)?) with Stop Loss at (\d+(\.\d+)?)/;
  const match = cleanMessage.match(regex);

  if (match) {
    return {
      transactionType: match[1],
      symbol: match[2],
      price: parseFloat(match[3]),
      stopLoss: parseFloat(match[5]),
    };
  }

  return null;
}

const BUY_ADJUSTMENT = 1.0025;
const SELL_ADJUSTMENT = 0.9975;

///////// All changes  are done successfully
module.exports = {
  createFakeResponse,
  sendMessageToTelegram,
  EangelparseMessageText,
  /// PRICES EDJUSTMENT VARIABLES//
  BUY_ADJUSTMENT,
  SELL_ADJUSTMENT,
};
