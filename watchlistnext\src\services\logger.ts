type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogOptions {
  level?: LogLevel;
  silent?: boolean;
  enableConsole?: boolean;
}

class Logger {
  private silent: boolean;
  private enableConsole: boolean;

  constructor(options: LogOptions = {}) {
    this.silent = options.silent || false;
    // Only enable console logging in development and for errors
    this.enableConsole = options.enableConsole ?? (process.env.NODE_ENV === 'development');
  }

  private log(level: LogLevel, message: string, ...args: any[]) {
    if (this.silent) return;

    // Only log errors and market summaries to console
    if (this.enableConsole && (level === 'error' || message.includes('Market Data Summary'))) {
      const timestamp = new Date().toISOString();
      const prefix = `[${timestamp}] ${level.toUpperCase()}:`;
      
      if (level === 'error') {
        console.error(prefix, message, ...args);
      } else {
        console.log(prefix, message, ...args);
      }
    }
  }

  debug(message: string, ...args: any[]) {
    // Debug messages are completely silent unless explicitly enabled
    if (process.env.DEBUG_LOGS === 'true') {
      this.log('debug', message, ...args);
    }
  }

  info(message: string, ...args: any[]) {
    // Only log market summaries and critical info
    if (message.includes('Market Data Summary') || process.env.DEBUG_LOGS === 'true') {
      this.log('info', message, ...args);
    }
  }

  warn(message: string, ...args: any[]) {
    // Only log critical warnings
    if (message.includes('error') || message.includes('fail') || message.includes('disconnect')) {
      this.log('warn', message, ...args);
    }
  }

  error(message: string, ...args: any[]) {
    this.log('error', message, ...args);
  }
}

export const logger = new Logger({
  silent: process.env.NODE_ENV === 'test',
  enableConsole: process.env.NODE_ENV === 'development'
});
