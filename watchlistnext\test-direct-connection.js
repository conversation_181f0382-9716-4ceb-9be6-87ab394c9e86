const { Client } = require('pg');

async function testDirectConnection() {
  console.log('🔍 Testing direct database connection...');
  
  // Test different connection approaches
  const connectionConfigs = [
    {
      name: "Environment Variable",
      config: {
        connectionString: process.env.POSTGRES_DATABASE_URL,
        ssl: { rejectUnauthorized: false }
      }
    },
    {
      name: "Direct URL Encoded",
      config: {
        connectionString: "postgresql://postgres.fjognbnryybyeepcoukr:<EMAIL>:5432/postgres",
        ssl: { rejectUnauthorized: false }
      }
    },
    {
      name: "Individual Parameters",
      config: {
        host: 'aws-0-ap-south-1.pooler.supabase.com',
        port: 5432,
        database: 'postgres',
        user: 'postgres.fjognbnryybyeepcoukr',
        password: 'Avisekh@5028',
        ssl: { rejectUnauthorized: false }
      }
    }
  ];

  for (const { name, config } of connectionConfigs) {
    console.log(`\n📋 Testing ${name}...`);
    
    const client = new Client(config);
    
    try {
      await client.connect();
      console.log(`✅ ${name} connection successful!`);
      
      const result = await client.query('SELECT COUNT(*) FROM company_list');
      console.log(`📊 Found ${result.rows[0].count} companies in database`);
      
      await client.end();
      console.log(`🎉 ${name} test completed successfully!`);
      return true; // Success, no need to test others
      
    } catch (error) {
      console.error(`❌ ${name} failed:`, error.message);
      try {
        await client.end();
      } catch (endError) {
        // Ignore end errors
      }
    }
  }
  
  return false;
}

testDirectConnection()
  .then(success => {
    if (success) {
      console.log('\n🎉 At least one connection method worked!');
      process.exit(0);
    } else {
      console.log('\n❌ All connection methods failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });
