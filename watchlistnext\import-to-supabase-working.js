const XLSX = require("xlsx");
const { Client } = require("pg");
require("dotenv").config();

console.log("🚀 Importing company data to Supabase database...");

// Use connection from environment variables
const workingConfig = {
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: { rejectUnauthorized: false },
  connectionTimeoutMillis: 15000,
};

async function readExcelFile() {
  console.log("📖 Reading Excel file: Companylist.xlsx");

  try {
    const workbook = XLSX.readFile("Companylist.xlsx");
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    console.log(`📊 Found ${data.length} companies in Excel file`);
    return data;
  } catch (error) {
    console.error("❌ Error reading Excel file:", error.message);
    throw error;
  }
}

async function createTable(client) {
  console.log("🏗️ Creating company_list table...");

  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS company_list (
      id SERIAL PRIMARY KEY,
      company_name VARCHAR(500) NOT NULL,
      isin_no VARCHAR(20),
      instrument VARCHAR(50),
      sector_name VARCHAR(200),
      industry_new_name VARCHAR(200),
      sub_sector VARCHAR(200),
      micro_category VARCHAR(200),
      bse_security_id VARCHAR(20),
      nse_security_id VARCHAR(20),
      symbol VARCHAR(50),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  await client.query(createTableQuery);
  console.log("✅ Table created successfully");
}

async function clearExistingData(client) {
  console.log("🗑️ Clearing existing data...");
  await client.query("DELETE FROM company_list");
  console.log("✅ Existing data cleared");
}

async function insertCompanies(client, companies) {
  console.log("💾 Inserting company data...");

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < companies.length; i++) {
    const company = companies[i];

    try {
      const nseSecurityId =
        company.NSE_SECURITY_ID === "-" || !company.NSE_SECURITY_ID
          ? null
          : company.NSE_SECURITY_ID.toString();
      const bseSecurityId = company.BSE_SECURITY_ID
        ? company.BSE_SECURITY_ID.toString()
        : null;

      await client.query(
        `
        INSERT INTO company_list (
          company_name, isin_no, instrument, sector_name, industry_new_name,
          sub_sector, micro_category, bse_security_id, nse_security_id, symbol
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `,
        [
          company.COMPANY_NAME || "",
          company["ISIN No"] || "",
          company.Instrument || "",
          company["Sector Name"] || "",
          company["Industry New Name"] || "",
          company["Sub-Sector"] || "",
          company["Micro Category"] || "",
          bseSecurityId,
          nseSecurityId,
          company.SYMBOL || "",
        ]
      );

      successCount++;

      // Progress update every 100 records
      if ((i + 1) % 100 === 0) {
        console.log(`📈 Processed ${i + 1} / ${companies.length} companies`);
      }
    } catch (error) {
      console.error(
        `❌ Error inserting ${company.COMPANY_NAME}:`,
        error.message
      );
      errorCount++;
    }
  }

  return { successCount, errorCount };
}

async function verifyImport(client) {
  console.log("🔍 Verifying import...");

  // Get total count
  const countResult = await client.query(
    "SELECT COUNT(*) as count FROM company_list"
  );
  console.log(`📊 Total companies in database: ${countResult.rows[0].count}`);

  // Get sample data
  const sampleResult = await client.query("SELECT * FROM company_list LIMIT 5");
  console.log("\n📋 Sample data from database:");
  console.table(sampleResult.rows);

  // Get sector statistics
  const sectorStats = await client.query(`
    SELECT sector_name, COUNT(*) as count 
    FROM company_list 
    WHERE sector_name IS NOT NULL AND sector_name != ''
    GROUP BY sector_name 
    ORDER BY count DESC 
    LIMIT 10
  `);
  console.log("\n📊 Top 10 sectors by company count:");
  console.table(sectorStats.rows);

  return countResult.rows[0].count;
}

async function main() {
  let client;

  try {
    // Read Excel file
    const companies = await readExcelFile();

    // Connect to database
    console.log("🔗 Connecting to Supabase database...");
    client = new Client(workingConfig);
    await client.connect();
    console.log("✅ Connected to database");

    // Create table
    await createTable(client);

    // Clear existing data
    await clearExistingData(client);

    // Insert companies
    const { successCount, errorCount } = await insertCompanies(
      client,
      companies
    );

    console.log(`\n✅ Successfully inserted: ${successCount} companies`);
    console.log(`❌ Errors: ${errorCount} companies`);

    // Verify import
    const totalRecords = await verifyImport(client);

    console.log("\n" + "=".repeat(60));
    console.log("🎉 SUPABASE IMPORT COMPLETED SUCCESSFULLY!");
    console.log("=".repeat(60));
    console.log(`📊 Total records imported: ${totalRecords}`);
    console.log(
      `✅ Success rate: ${((successCount / companies.length) * 100).toFixed(1)}%`
    );
    console.log("\n🚀 Your Supabase database is now ready with company data!");
    console.log("🔗 You can now use this data in your application");
  } catch (error) {
    console.error("❌ Import process failed:", error.message);
    console.error("Full error:", error);
    process.exit(1);
  } finally {
    if (client) {
      try {
        await client.end();
        console.log("🔌 Database connection closed");
      } catch (e) {
        // Ignore cleanup errors
      }
    }
  }
}

main();
