"use client";

import React from 'react';

interface StatsProps {
  totalInstruments: number;
  filteredInstruments: number;
  marketDataCount: number;
  connected: boolean;
}

const Stats: React.FC<StatsProps> = ({
  totalInstruments,
  filteredInstruments,
  marketDataCount,
  connected,
}) => {
  const stats = [
    {
      label: 'Total Instruments',
      value: totalInstruments.toLocaleString(),
      icon: '📊',
      color: 'bg-blue-500',
    },
    {
      label: 'Filtered Results',
      value: filteredInstruments.toLocaleString(),
      icon: '🔍',
      color: 'bg-purple-500',
    },
    {
      label: 'Live Data',
      value: marketDataCount.toLocaleString(),
      icon: '📈',
      color: connected ? 'bg-green-500' : 'bg-gray-500',
    },
    {
      label: 'Connection',
      value: connected ? 'Active' : 'Inactive',
      icon: connected ? '🟢' : '🔴',
      color: connected ? 'bg-green-500' : 'bg-red-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => (
        <div key={index} className="glass rounded-xl shadow-lg p-4 card-hover">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{stat.label}</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
            </div>
            <div className="text-2xl">{stat.icon}</div>
          </div>
          <div className={`mt-3 h-1 rounded-full ${stat.color}`} />
        </div>
      ))}
    </div>
  );
};

export default Stats;
