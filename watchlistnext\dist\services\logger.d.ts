type LogLevel = 'debug' | 'info' | 'warn' | 'error';
interface LogOptions {
    level?: LogLevel;
    silent?: boolean;
    enableConsole?: boolean;
}
declare class Logger {
    private silent;
    private enableConsole;
    constructor(options?: LogOptions);
    private log;
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
export declare const logger: Logger;
export {};
//# sourceMappingURL=logger.d.ts.map