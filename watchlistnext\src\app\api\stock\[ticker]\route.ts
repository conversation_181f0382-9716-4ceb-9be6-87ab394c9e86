export async function GET(
  request: Request,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const resolvedParams = await params;
    const ticker = resolvedParams.ticker;

    // Get exchange parameter from URL
    const { searchParams } = new URL(request.url);
    const exchange = searchParams.get("exchange");

    if (!ticker) {
      return new Response(
        JSON.stringify({ error: "Ticker parameter is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Fetch data directly from the WebSocket server's stock endpoint
    try {
      const exchangeParam = exchange ? `?exchange=${exchange}` : "";
      const response = await fetch(
        `http://localhost:8080/api/stock/${ticker}${exchangeParam}`,
        {
          signal: AbortSignal.timeout(5000),
        }
      );

      if (response.ok) {
        const stock = await response.json();
        return new Response(JSON.stringify(stock), {
          headers: { "Content-Type": "application/json" },
        });
      } else if (response.status === 404) {
        const errorData = await response.json();
        return new Response(JSON.stringify(errorData), {
          status: 404,
          headers: { "Content-Type": "application/json" },
        });
      } else {
        throw new Error(`Server responded with status: ${response.status}`);
      }
    } catch (error) {
      console.error("Error fetching from WebSocket server:", error);

      // Return a fallback error response
      return new Response(
        JSON.stringify({
          error: "Stock not found",
          message: `Could not find stock with ticker "${ticker}". The market data server may be unavailable.`,
          suggestions: [
            "Check if the ticker symbol is correct",
            "Make sure the market data server is running",
            "Try using the exact NSE/BSE ticker symbol",
          ],
          ticker: ticker,
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  } catch (error) {
    console.error("Error fetching stock detail:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
