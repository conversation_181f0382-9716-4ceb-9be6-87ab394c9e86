import { workingDataService } from "@/services/WorkingDataService";
import { logger } from "@/services/LoggerService";

// Simple in-memory cache
const cache = new Map();
const CACHE_DURATION = 30000; // 30 seconds

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const exchange = searchParams.get("exchange") || "NSE";
    const limit = parseInt(searchParams.get("limit") || "1000");

    // Check cache first
    const cacheKey = `sectors_${exchange}_${limit}`;
    const cached = cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return new Response(JSON.stringify(cached.data), {
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "public, max-age=30"
        }
      });
    }

    // Get all instruments with their current market data
    const response = await fetch(`http://localhost:8080/api/all-instruments?exchange=${exchange}_EQ`);
    if (!response.ok) {
      throw new Error(`Failed to fetch market data: ${response.statusText}`);
    }

    const marketData = await response.json();

    // Get company information for sector mapping
    const companies = await workingDataService.getCompaniesByExchange(exchange, limit);

    // Create a map of symbol to company info for quick lookup
    const companyMap = new Map(
      companies.map((company: any) => [company.symbol, company])
    );

    // Group instruments by sector
    const sectorMap = new Map();

    marketData.instruments?.forEach((instrument: any) => {
      const companyInfo = companyMap.get(instrument.ticker);
      const sector = companyInfo?.sector_name || "Others";

      if (!sectorMap.has(sector)) {
        sectorMap.set(sector, {
          sector,
          stocks: [],
          totalStocks: 0,
          gainers: 0,
          losers: 0,
          avgChange: 0,
          totalVolume: 0
        });
      }

      const sectorData = sectorMap.get(sector);
      sectorData.stocks.push({
        ...instrument,
        companyName: companyInfo?.company_name || instrument.ticker,
        sector,
        industry: companyInfo?.industry_new_name || "Unknown"
      });

      // Update sector statistics
      sectorData.totalStocks++;
      if (instrument.change > 0) sectorData.gainers++;
      if (instrument.change < 0) sectorData.losers++;
      sectorData.totalVolume += instrument.volume || 0;
    });

    // Calculate average changes and sort stocks
    Array.from(sectorMap.values()).forEach(sectorData => {
      sectorData.avgChange = sectorData.stocks.reduce((sum: number, stock: any) => 
        sum + (stock.changePercent || 0), 0) / sectorData.totalStocks;

      // Sort stocks by absolute change percentage
      sectorData.stocks.sort((a: any, b: any) => 
        Math.abs(b.changePercent || 0) - Math.abs(a.changePercent || 0)
      );
    });

    // Convert to array and sort sectors by average change
    const sectors = Array.from(sectorMap.values()).sort((a, b) => 
      Math.abs(b.avgChange) - Math.abs(a.avgChange)
    );

    const responseData = {
      success: true,
      connected: marketData.connected,
      sectors,
      stats: {
        totalSectors: sectors.length,
        totalStocks: marketData.instruments?.length || 0,
        liveStocks: marketData.liveInstruments || 0,
        timestamp: Date.now()
      }
    };

    // Cache the response
    cache.set(cacheKey, {
      data: responseData,
      timestamp: Date.now()
    });

    return new Response(JSON.stringify(responseData), {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=30"
      }
    });
  } catch (error) {
    logger.error("Error in sectors API", {
      error: (error as Error).message
    });

    return new Response(JSON.stringify({
      success: false,
      error: "Failed to fetch sector data",
      message: (error as Error).message,
      timestamp: Date.now()
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
}
