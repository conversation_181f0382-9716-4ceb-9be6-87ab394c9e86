import { io, Socket } from "socket.io-client";
import { logger } from "../services/logger";

interface WebSocketOptions {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onReconnect?: (attemptNumber: number) => void;
  onMarketData?: (data: any) => void;
  onMarketDataBatch?: (data: any[]) => void;
}

// Global socket instance to prevent multiple connections
let globalSocket: Socket | null = null;
let connectionCount = 0;
let isConnecting = false;
let listeners: Map<string, ((...args: any[]) => void)[]> = new Map();

export const createWebSocketConnection = (
  options: WebSocketOptions = {}
): Socket => {
  // Reuse existing connection if available and connected
  if (globalSocket && globalSocket.connected) {
    connectionCount++;
    
    // Add new listeners without removing existing ones
    if (options.onMarketData) {
      if (!listeners.has("marketData")) {
        listeners.set("marketData", []);
      }
      listeners.get("marketData")!.push(options.onMarketData);
      globalSocket.on("marketData", options.onMarketData);
    }
    
    if (options.onMarketDataBatch) {
      if (!listeners.has("marketDataBatch")) {
        listeners.set("marketDataBatch", []);
      }
      listeners.get("marketDataBatch")!.push(options.onMarketDataBatch);
      globalSocket.on("marketDataBatch", options.onMarketDataBatch);
    }

    return globalSocket;
  }

  // Prevent multiple connection attempts
  if (isConnecting) {
    // Return a promise-like object that resolves when connection is ready
    return new Promise((resolve) => {
      const checkConnection = () => {
        if (globalSocket && globalSocket.connected) {
          resolve(globalSocket);
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      checkConnection();
    }) as any;
  }

  // Disconnect existing socket if it exists but is not connected
  if (globalSocket && !globalSocket.connected) {
    globalSocket.removeAllListeners();
    globalSocket.disconnect();
    globalSocket = null;
    listeners.clear();
  }

  isConnecting = true;
  connectionCount = 1;
  listeners.clear();

  // Helper function to get server URL safely
  const getServerUrl = (): string => {
    // Check if we're in a browser environment (client-side)
    const isBrowser = typeof window !== 'undefined';
    
    if (isBrowser && process.env.NODE_ENV === 'production') {
      // In production browser environment, use current host
      try {
        return `${window.location.protocol}//${window.location.hostname}:8080`;
      } catch {
        // Fallback if window access fails
        return "http://localhost:8080";
      }
    }
    
    // In development or server-side, always use localhost
    return "http://localhost:8080";
  };

  const serverUrl = getServerUrl();

  const socketInstance = io(serverUrl, {
    transports: ["websocket", "polling"], // Allow fallback to polling
    upgrade: true,
    rememberUpgrade: false,
    timeout: 15000, // Increased timeout
    forceNew: false,
    reconnection: true,
    reconnectionAttempts: 10, // Increased attempts
    reconnectionDelay: 2000,
    reconnectionDelayMax: 10000,
    randomizationFactor: 0.5,
  });

  globalSocket = socketInstance;

  socketInstance.on("connect", () => {
    logger.info(`Connected to real-time feed (${connectionCount} clients)`);
    isConnecting = false;
    options.onConnect?.();
    
    // Request initial data only once
    if (connectionCount === 1) {
      socketInstance.emit("requestInitialData");
    }
  });

  socketInstance.on("disconnect", (reason) => {
    logger.warn(`Disconnected from real-time feed: ${reason}`);
    isConnecting = false;
    connectionCount = Math.max(0, connectionCount - 1);
    options.onDisconnect?.(reason);

    // Handle different disconnect reasons
    if (reason === "io server disconnect") {
      logger.info("Server initiated disconnect - will reconnect automatically");
    } else if (reason === "transport close" || reason === "transport error") {
      logger.info("Connection lost - attempting to reconnect...");
    }
  });

  socketInstance.on("connect_error", (error) => {
    logger.error("WebSocket connection error:", error);
    isConnecting = false;
    options.onError?.(error);
  });

  socketInstance.on("reconnect", (attemptNumber) => {
    logger.info(`Reconnected after ${attemptNumber} attempts`);
    isConnecting = false;
    options.onReconnect?.(attemptNumber);
  });

  socketInstance.on("reconnect_error", (error) => {
    logger.error("Reconnection failed:", error);
  });

  socketInstance.on("reconnect_failed", () => {
    logger.error("All reconnection attempts failed");
    isConnecting = false;
  });

  // Store and add market data listeners
  if (options.onMarketData) {
    if (!listeners.has("marketData")) {
      listeners.set("marketData", []);
    }
    listeners.get("marketData")!.push(options.onMarketData);
    socketInstance.on("marketData", options.onMarketData);
  }

  if (options.onMarketDataBatch) {
    if (!listeners.has("marketDataBatch")) {
      listeners.set("marketDataBatch", []);
    }
    listeners.get("marketDataBatch")!.push(options.onMarketDataBatch);
    socketInstance.on("marketDataBatch", options.onMarketDataBatch);
  }

  // Add error handling for malformed data
  socketInstance.on("error", (error) => {
    logger.error("Socket error:", error);
    options.onError?.(error);
  });

  return socketInstance;
};

// Function to remove specific listeners for a client
export const removeWebSocketListeners = (
  onMarketData?: (...args: any[]) => void,
  onMarketDataBatch?: (...args: any[]) => void
): void => {
  if (globalSocket) {
    if (onMarketData) {
      globalSocket.off("marketData", onMarketData);
      const marketDataListeners = listeners.get("marketData");
      if (marketDataListeners) {
        const index = marketDataListeners.indexOf(onMarketData);
        if (index > -1) {
          marketDataListeners.splice(index, 1);
        }
      }
    }
    
    if (onMarketDataBatch) {
      globalSocket.off("marketDataBatch", onMarketDataBatch);
      const batchListeners = listeners.get("marketDataBatch");
      if (batchListeners) {
        const index = batchListeners.indexOf(onMarketDataBatch);
        if (index > -1) {
          batchListeners.splice(index, 1);
        }
      }
    }
    
    connectionCount = Math.max(0, connectionCount - 1);
    logger.debug(`Removed client listeners (${connectionCount} clients remaining)`);
  }
};

// Function to properly cleanup WebSocket connection (only when no clients)
export const cleanupWebSocketConnection = (): void => {
  if (globalSocket && connectionCount === 0) {
    logger.info("Cleaning up WebSocket connection - no clients remaining");
    globalSocket.removeAllListeners();
    globalSocket.disconnect();
    globalSocket = null;
    listeners.clear();
  }
};

// Function to get connection status
export const getWebSocketStatus = (): {
  connected: boolean;
  clients: number;
} => {
  return {
    connected: globalSocket?.connected || false,
    clients: connectionCount,
  };
};
