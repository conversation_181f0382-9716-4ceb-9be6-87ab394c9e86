import { config as dotenvConfig } from "dotenv";
import { z } from "zod";

// Load environment variables
dotenvConfig();

// Define configuration schema
const configSchema = z.object({
  // Database
  database: z.object({
    url: z.string().url("Invalid database URL"),
    ssl: z.boolean().default(false),
    maxConnections: z.number().default(20),
    idleTimeout: z.number().default(30000),
    connectionTimeout: z.number().default(5000),
  }),

  // API
  api: z.object({
    port: z.number().default(3000),
    accessToken: z.string().min(1, "Access token is required"),
    clientId: z.string().min(1, "Client ID is required"),
  }),

  // WebSocket
  websocket: z.object({
    subscriptionType: z.enum(["ticker", "quote", "full"]).default("quote"),
    maxInstruments: z.number().default(25000),
    preferredExchange: z.enum(["NSE", "BSE"]).default("NSE"),
    heartbeatInterval: z.number().default(30000),
    reconnectAttempts: z.number().default(5),
    reconnectDelay: z.number().default(5000),
  }),

  // Security
  security: z.object({
    allowedOrigins: z
      .array(z.string().url())
      .default(["http://localhost:3000"]),
    rateLimitWindowMs: z.number().default(900000), // 15 minutes
    rateLimitMaxRequests: z.number().default(100),
  }),

  // File Upload
  upload: z.object({
    maxFileSize: z.number().default(5242880), // 5MB
    uploadDir: z.string().default("uploads"),
    allowedExtensions: z.array(z.string()).default([".xlsx", ".xls"]),
  }),

  // Logging
  logging: z.object({
    level: z.enum(["error", "warn", "info", "debug"]).default("info"),
  }),
});

// Parse and validate configuration
const parseConfig = () => {
  try {
    return configSchema.parse({
      database: {
        url: process.env.POSTGRES_DATABASE_URL,
        ssl: process.env.POSTGRES_SSL === "true",
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || "20"),
        idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || "30000"),
        connectionTimeout: parseInt(
          process.env.DB_CONNECTION_TIMEOUT || "5000"
        ),
      },
      api: {
        port: parseInt(process.env.PORT || "3000"),
        accessToken: process.env.ACCESS_TOKEN,
        clientId: process.env.CLIENT_ID,
      },
      websocket: {
        subscriptionType: process.env.SUBSCRIPTION_TYPE || "quote",
        maxInstruments: parseInt(process.env.MAX_INSTRUMENTS || "25000"),
        preferredExchange: process.env.PREFERRED_EXCHANGE || "NSE",
        heartbeatInterval: parseInt(
          process.env.WS_HEARTBEAT_INTERVAL || "30000"
        ),
        reconnectAttempts: parseInt(process.env.WS_RECONNECT_ATTEMPTS || "5"),
        reconnectDelay: parseInt(process.env.WS_RECONNECT_DELAY || "5000"),
      },
      security: {
        allowedOrigins: process.env.ALLOWED_ORIGINS?.split(",") || [
          "http://localhost:3000",
          "http://localhost:3001",
        ],
        rateLimitWindowMs: parseInt(
          process.env.RATE_LIMIT_WINDOW_MS || "900000"
        ),
        rateLimitMaxRequests: parseInt(
          process.env.RATE_LIMIT_MAX_REQUESTS || "100"
        ),
      },
      upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE || "5242880"),
        uploadDir: process.env.UPLOAD_DIR || "uploads",
        allowedExtensions: process.env.ALLOWED_EXTENSIONS?.split(",") || [
          ".xlsx",
          ".xls",
        ],
      },
      logging: {
        level: process.env.LOG_LEVEL || "info",
      },
    });
  } catch (error: unknown) {
    if (error instanceof z.ZodError) {
      console.error("Configuration validation failed:", error.errors);
    } else {
      console.error("Failed to parse configuration:", error);
    }
    process.exit(1);
  }
};

export const appConfig = parseConfig();
