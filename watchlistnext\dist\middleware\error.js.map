{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/middleware/error.ts"], "names": [], "mappings": ";;;AACA,6BAA+B;AAC/B,sCAAsC;AAEtC,oCAAoC;AACpC,MAAa,QAAS,SAAQ,KAAK;IACjC,YACS,UAAkB,EACzB,OAAe,EACR,IAAa,EACb,OAAa;QAEpB,KAAK,CAAC,OAAO,CAAC,CAAC;QALR,eAAU,GAAV,UAAU,CAAQ;QAElB,SAAI,GAAJ,IAAI,CAAS;QACb,YAAO,GAAP,OAAO,CAAM;QAGpB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;IACzB,CAAC;CACF;AAVD,4BAUC;AAED,2BAA2B;AACpB,MAAM,YAAY,GAAG,CAC1B,GAAU,EACV,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE;QACtB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,kBAAS,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;KACnE,CAAC,CAAC;IAEH,+BAA+B;IAC/B,IAAI,GAAG,YAAY,cAAQ,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,kBAAkB;YACzB,OAAO,EAAE,GAAG,CAAC,MAAM;SACpB,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB;IACpB,IAAI,GAAG,YAAY,QAAQ,EAAE,CAAC;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;YACrC,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,GAAG,CAAC,OAAO;YAClB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;IAED,yBAAyB;IACzB,IAAI,GAAG,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QACjC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,gDAAgD;SAC1D,CAAC,CAAC;IACL,CAAC;IAED,0BAA0B;IAC1B,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QAClC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,0CAA0C;SACpD,CAAC,CAAC;IACL,CAAC;IAED,4BAA4B;IAC5B,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QAC/B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,mBAAmB;YAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,uBAAuB;QAC9B,OAAO,EACL,kBAAS,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO;YACjC,CAAC,CAAC,GAAG,CAAC,OAAO;YACb,CAAC,CAAC,8BAA8B;KACrC,CAAC,CAAC;AACL,CAAC,CAAC;AAnEW,QAAA,YAAY,gBAmEvB;AAEF,+BAA+B;AACxB,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;IAC7D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QACnB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,WAAW;QAClB,OAAO,EAAE,0BAA0B,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,gBAAgB;KACjF,CAAC,CAAC;AACL,CAAC,CAAC;AANW,QAAA,eAAe,mBAM1B;AAEF,8CAA8C;AACvC,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAEF,2BAA2B;AACpB,MAAM,eAAe,GAAG,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE;IAChE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF,+BAA+B;AACxB,MAAM,SAAS,GAAG,CAAC,UAAkB,uBAAuB,EAAE,EAAE;IACrE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;AAClD,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF,8BAA8B;AACvB,MAAM,cAAc,GAAG,CAAC,UAAkB,eAAe,EAAE,EAAE;IAClE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AACjD,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF,mCAAmC;AAC5B,MAAM,aAAa,GAAG,CAAC,UAAkB,oBAAoB,EAAE,EAAE;IACtE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AACjD,CAAC,CAAC;AAFW,QAAA,aAAa,iBAExB;AAEF,2BAA2B;AACpB,MAAM,cAAc,GAAG,CAAC,UAAkB,mBAAmB,EAAE,EAAE;IACtE,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAC3D,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF,yBAAyB;AAClB,MAAM,aAAa,GAAG,CAC3B,UAAkB,2BAA2B,EAC7C,EAAE;IACF,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;AACtD,CAAC,CAAC;AAJW,QAAA,aAAa,iBAIxB;AAEF,0BAA0B;AACnB,MAAM,cAAc,GAAG,CAC5B,UAAkB,4BAA4B,EAC9C,EAAE;IACF,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACvD,CAAC,CAAC;AAJW,QAAA,cAAc,kBAIzB"}