import { CacheManager } from "@/types";
export declare class CacheService implements CacheManager {
    private cache;
    private maxSize;
    private defaultTTL;
    private cleanupInterval;
    constructor(maxSize?: number, defaultTTL?: number);
    /**
     * Get value from cache
     */
    get<T>(key: string): T | null;
    /**
     * Set value in cache
     */
    set<T>(key: string, value: T, ttl?: number): void;
    /**
     * Delete specific key from cache
     */
    delete(key: string): boolean;
    /**
     * Clear all cache entries
     */
    clear(): void;
    /**
     * Get current cache size
     */
    size(): number;
    /**
     * Get cache statistics
     */
    getStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        memoryUsage: number;
    };
    /**
     * Check if cache entry has expired
     */
    private isExpired;
    /**
     * Evict oldest entries when cache is full
     */
    private evictOldest;
    /**
     * Start periodic cleanup of expired entries
     */
    private startCleanupInterval;
    /**
     * Remove all expired entries
     */
    private cleanupExpired;
    /**
     * Calculate cache hit rate (simplified)
     */
    private calculateHitRate;
    /**
     * Estimate memory usage (simplified)
     */
    private estimateMemoryUsage;
    /**
     * Cleanup resources
     */
    destroy(): void;
    /**
     * Get all keys matching a pattern
     */
    getKeys(pattern?: RegExp): string[];
    /**
     * Check if key exists in cache
     */
    has(key: string): boolean;
    /**
     * Get multiple values at once
     */
    getMultiple<T>(keys: string[]): Map<string, T | null>;
    /**
     * Set multiple values at once
     */
    setMultiple<T>(entries: Map<string, T>, ttl?: number): void;
    /**
     * Increment a numeric value in cache
     */
    increment(key: string, delta?: number): number;
    /**
     * Decrement a numeric value in cache
     */
    decrement(key: string, delta?: number): number;
}
export declare const cacheService: CacheService;
export declare const CacheKeys: {
    marketData: (securityId: string) => string;
    indices: (limit: number) => string;
    watchlist: (id: number) => string;
    watchlistItems: (id: number) => string;
    health: () => string;
    instruments: () => string;
    subscriptions: () => string;
};
export default CacheService;
//# sourceMappingURL=CacheService.d.ts.map