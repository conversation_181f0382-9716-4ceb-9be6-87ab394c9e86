"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";

interface StockData {
  ticker: string;
  securityId: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  companyName?: string;
  sector?: string;
}

interface CompanyInfo {
  company_name: string;
  symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_new_name?: string;
}

export default function SimpleDashboard() {
  const [stocks, setStocks] = useState<StockData[]>([]);
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>(
    {}
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch companies and market data in parallel
        const [companiesRes, marketRes] = await Promise.all([
          fetch("/api/companies?limit=1000"),
          fetch("/api/data?exchange=NSE_EQ&limit=1000"),
        ]);

        // Process companies
        let companyMap: { [key: string]: CompanyInfo } = {};
        if (companiesRes.ok) {
          const companiesData = await companiesRes.json();
          if (companiesData.success) {
            companiesData.data.forEach((company: any) => {
              // Map by security IDs (this is how websocket data matches)
              if (company.nse_security_id) {
                companyMap[company.nse_security_id] = company;
              }
              if (company.bse_security_id) {
                companyMap[company.bse_security_id] = company;
              }
              // Also map by symbol as fallback
              if (company.symbol) {
                companyMap[company.symbol] = company;
              }
            });
            setCompanies(companyMap);
          }
        }

        // Process market data
        if (marketRes.ok) {
          const marketData = await marketRes.json();
          if (marketData.latestData) {
            const stocksWithCompanyInfo = marketData.latestData
              .filter((stock: any) => stock && stock.ticker && stock.ltp)
              .map((stock: any) => {
                // Try to find company by security ID first, then by symbol
                const company =
                  companyMap[stock.securityId] || companyMap[stock.ticker];
                return {
                  ticker: stock.ticker,
                  securityId: stock.securityId,
                  ltp: stock.ltp || 0,
                  change: stock.change || 0,
                  changePercent: stock.changePercent || 0,
                  volume: stock.volume || 0,
                  companyName: company?.company_name || stock.ticker,
                  sector: company?.sector_name || "Unknown",
                };
              })
              .slice(0, 50); // Show top 50 stocks

            setStocks(stocksWithCompanyInfo);
          }
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Refresh every 5 seconds
    const interval = setInterval(fetchData, 5000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-xl">Loading market data...</div>
      </div>
    );
  }

  const gainers = stocks.filter((s) => s.change > 0).length;
  const losers = stocks.filter((s) => s.change < 0).length;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Simple Market Dashboard</h1>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stocks.length}</div>
            <div className="text-sm text-gray-600">Total Stocks</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{gainers}</div>
            <div className="text-sm text-gray-600">Gainers</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{losers}</div>
            <div className="text-sm text-gray-600">Losers</div>
          </CardContent>
        </Card>
      </div>

      {/* Stocks Table */}
      <Card>
        <CardHeader>
          <CardTitle>Live Market Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Symbol</th>
                  <th className="text-left p-2">Company</th>
                  <th className="text-left p-2">Sector</th>
                  <th className="text-right p-2">Price</th>
                  <th className="text-right p-2">Change</th>
                  <th className="text-right p-2">Volume</th>
                </tr>
              </thead>
              <tbody>
                {stocks.map((stock) => (
                  <tr
                    key={stock.securityId}
                    className="border-b hover:bg-gray-50 cursor-pointer"
                    onClick={() =>
                      window.open(`/simple-stock/${stock.ticker}`, "_blank")
                    }
                  >
                    <td className="p-2 font-semibold text-blue-600 hover:text-blue-800">
                      {stock.ticker}
                    </td>
                    <td className="p-2 text-sm">{stock.companyName}</td>
                    <td className="p-2 text-sm">{stock.sector}</td>
                    <td className="p-2 text-right">₹{stock.ltp.toFixed(2)}</td>
                    <td
                      className={`p-2 text-right ${stock.change >= 0 ? "text-green-600" : "text-red-600"}`}
                    >
                      {stock.change >= 0 ? "+" : ""}
                      {stock.change.toFixed(2)} (
                      {stock.changePercent.toFixed(2)}%)
                    </td>
                    <td className="p-2 text-right text-sm">
                      {stock.volume.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
