const { Pool } = require("pg");
require("dotenv").config();

const pool = new Pool({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false,
  },
});

async function createWatchlistTables() {
  const client = await pool.connect();
  
  try {
    console.log("🔄 Creating watchlist tables...");

    // Create watchlists table
    await client.query(`
      CREATE TABLE IF NOT EXISTS watchlists (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log("✅ Created watchlists table");

    // Create sector_watchlist table
    await client.query(`
      CREATE TABLE IF NOT EXISTS sector_watchlist (
        id SERIAL PRIMARY KEY,
        watchlist_id INTEGER REFERENCES watchlists(id) ON DELETE CASCADE,
        sector_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(watchlist_id, sector_name)
      );
    `);
    console.log("✅ Created sector_watchlist table");

    // Create industry_watchlist table
    await client.query(`
      CREATE TABLE IF NOT EXISTS industry_watchlist (
        id SERIAL PRIMARY KEY,
        watchlist_id INTEGER REFERENCES watchlists(id) ON DELETE CASCADE,
        industry_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(watchlist_id, industry_name)
      );
    `);
    console.log("✅ Created industry_watchlist table");

    // Create sub_sector_watchlist table
    await client.query(`
      CREATE TABLE IF NOT EXISTS sub_sector_watchlist (
        id SERIAL PRIMARY KEY,
        watchlist_id INTEGER REFERENCES watchlists(id) ON DELETE CASCADE,
        sub_sector_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(watchlist_id, sub_sector_name)
      );
    `);
    console.log("✅ Created sub_sector_watchlist table");

    // Create micro_category_watchlist table
    await client.query(`
      CREATE TABLE IF NOT EXISTS micro_category_watchlist (
        id SERIAL PRIMARY KEY,
        watchlist_id INTEGER REFERENCES watchlists(id) ON DELETE CASCADE,
        micro_category_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(watchlist_id, micro_category_name)
      );
    `);
    console.log("✅ Created micro_category_watchlist table");

    // Create indexes for better performance
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_sector_watchlist_watchlist_id ON sector_watchlist(watchlist_id);
      CREATE INDEX IF NOT EXISTS idx_industry_watchlist_watchlist_id ON industry_watchlist(watchlist_id);
      CREATE INDEX IF NOT EXISTS idx_sub_sector_watchlist_watchlist_id ON sub_sector_watchlist(watchlist_id);
      CREATE INDEX IF NOT EXISTS idx_micro_category_watchlist_watchlist_id ON micro_category_watchlist(watchlist_id);
      CREATE INDEX IF NOT EXISTS idx_watchlists_type ON watchlists(type);
    `);
    console.log("✅ Created indexes");

    // Insert some sample watchlists
    const sampleWatchlists = [
      { name: "My Sectors", type: "sector" },
      { name: "Favorite Industries", type: "industry" },
      { name: "Tech Sub-Sectors", type: "sub-sector" },
      { name: "Banking Micro-Categories", type: "micro-category" },
    ];

    for (const watchlist of sampleWatchlists) {
      await client.query(
        `INSERT INTO watchlists (name, type) VALUES ($1, $2) ON CONFLICT DO NOTHING`,
        [watchlist.name, watchlist.type]
      );
    }
    console.log("✅ Inserted sample watchlists");

    console.log("🎉 All watchlist tables created successfully!");

  } catch (error) {
    console.error("❌ Error creating watchlist tables:", error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await createWatchlistTables();
    console.log("✅ Database setup completed successfully!");
  } catch (error) {
    console.error("❌ Database setup failed:", error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { createWatchlistTables };
