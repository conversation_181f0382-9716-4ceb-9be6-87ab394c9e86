import "dotenv/config";
export declare class DhanMarketFeedServer {
    private accessToken;
    private clientId;
    private subscriptionType;
    private port;
    private ws;
    private isConnected;
    private messageCount;
    private instruments;
    private liveData;
    private app;
    private server;
    private io;
    private connectionAttempts;
    private maxReconnectAttempts;
    private prismaService;
    constructor();
    /**
     * Validate configuration
     */
    private validateConfig;
    /**
     * Load instruments from database
     */
    private loadInstruments;
    /**
     * Setup web server routes
     */
    private setupWebServer;
    /**
     * Setup Socket.IO handlers
     */
    private setupSocketHandlers;
    /**
     * Connect to Dhan WebSocket feed
     */
    connectToMarketFeed(): Promise<void>;
    /**
     * Subscribe to instruments (ENHANCED: Better BSE handling)
     */
    private subscribeToInstruments;
    /**
     * Validate BSE instruments for common issues
     */
    private validateBSEInstruments;
    /**
     * Subscribe to a batch of instruments (FIXED: Use STRING format like working implementation)
     */
    private subscribeToBatch;
    private lastSummaryTime;
    private summaryStats;
    /**
     * Handle incoming REAL market data from Dhan WebSocket
     */
    private handleMarketData;
    /**
     * Log market data summary
     */
    private logMarketDataSummary;
    /**
     * Reset summary statistics
     */
    private resetSummaryStats;
    /**
     * Parse quote packet data
     */
    private parseQuotePacket;
    /**
     * Parse full packet data
     */
    private parseFullPacket;
    /**
     * Calculate price change and percentage
     */
    private calculatePriceChange;
    /**
     * Parse market data from binary buffer
     */
    private parseMarketData;
    /**
     * Get exchange segment name from code
     */
    private getExchangeSegmentName;
    private calculateMarketCap;
    private getSector;
    private getIndustry;
    /**
     * Start the server
     */
    start(): Promise<void>;
    /**
     * Start health monitoring
     */
    private startHealthMonitoring;
    /**
     * Graceful shutdown
     */
    shutdown(): Promise<void>;
    /**
     * Get server status
     */
    getStatus(): {
        connected: boolean;
        instruments: number;
        messages: number;
        uptime: number;
        memory: number;
    };
}
//# sourceMappingURL=MarketFeedServer.d.ts.map