// ============================================================================
// CSV SERVICE - Parse and manage instrument data from CSV files
// ============================================================================

import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { 
  CSVInstrument, 
  Instrument, 
  InstrumentFilter, 
  SearchResult, 
  CSVService as ICSVService,
  CacheEntry 
} from '@/types';

// Exchange segment mapping for Dhan API
const EXCHANGE_SEGMENTS = {
  'NSE_EQ': 1,
  'NSE_FNO': 2,
  'BSE_EQ': 3,
  'BSE_FNO': 4,
  'MCX_COMM': 5,
  'IDX_I': 6,
} as const;

export class CSVService implements ICSVService {
  private instruments: Instrument[] = [];
  private cache: Map<string, CacheEntry<any>> = new Map();
  private csvFilePath: string;
  private cacheTTL: number;
  private lastLoadTime: number = 0;

  constructor(csvFilePath?: string, cacheTTL: number = 3600000) {
    this.csvFilePath = csvFilePath || process.env.CSV_FILE_PATH || './instruments.csv';
    this.cacheTTL = cacheTTL;
  }

  /**
   * Load instruments from CSV file
   */
  async loadInstruments(): Promise<Instrument[]> {
    const cacheKey = 'all_instruments';
    const cached = this.getFromCache<Instrument[]>(cacheKey);
    
    if (cached && Date.now() - this.lastLoadTime < this.cacheTTL) {
      this.instruments = cached;
      return this.instruments;
    }

    console.log(`📊 Loading instruments from CSV: ${this.csvFilePath}`);
    const startTime = Date.now();

    try {
      const csvData = await this.parseCSVFile();
      this.instruments = this.processCSVData(csvData);
      
      // Cache the results
      this.setCache(cacheKey, this.instruments);
      this.lastLoadTime = Date.now();

      const loadTime = Date.now() - startTime;
      console.log(`✅ Loaded ${this.instruments.length} instruments in ${loadTime}ms`);
      
      this.logInstrumentStats();
      
      return this.instruments;
    } catch (error) {
      console.error('❌ Error loading instruments from CSV:', error);
      throw new Error(`Failed to load instruments: ${(error as Error).message}`);
    }
  }

  /**
   * Parse CSV file and return raw data
   */
  private async parseCSVFile(): Promise<CSVInstrument[]> {
    return new Promise((resolve, reject) => {
      const results: CSVInstrument[] = [];
      const filePath = path.resolve(this.csvFilePath);

      if (!fs.existsSync(filePath)) {
        reject(new Error(`CSV file not found: ${filePath}`));
        return;
      }

      fs.createReadStream(filePath)
        .pipe(csv({
          skipEmptyLines: true,
          headers: true,
        }))
        .on('data', (data: CSVInstrument) => {
          results.push(data);
        })
        .on('end', () => {
          console.log(`📄 Parsed ${results.length} rows from CSV`);
          resolve(results);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  /**
   * Process raw CSV data into normalized instruments
   */
  private processCSVData(csvData: CSVInstrument[]): Instrument[] {
    const instruments: Instrument[] = [];
    let processed = 0;
    let skipped = 0;

    for (const row of csvData) {
      try {
        const instrument = this.normalizeInstrument(row);
        if (instrument) {
          instruments.push(instrument);
          processed++;
        } else {
          skipped++;
        }
      } catch (error) {
        skipped++;
        console.warn(`⚠️ Skipped invalid instrument row:`, error);
      }
    }

    console.log(`✅ Processed: ${processed}, Skipped: ${skipped}`);
    return instruments;
  }

  /**
   * Normalize CSV row to Instrument object
   */
  private normalizeInstrument(row: CSVInstrument): Instrument | null {
    // Skip invalid rows
    if (!row.SECURITY_ID || !row.SYMBOL_NAME || !row.EXCH_ID) {
      return null;
    }

    // Determine exchange segment
    const exchange = this.getExchangeSegment(row.EXCH_ID, row.SEGMENT);
    const exchangeCode = EXCHANGE_SEGMENTS[exchange as keyof typeof EXCHANGE_SEGMENTS] || 0;

    // Parse numeric values
    const lotSize = this.parseNumber(row.LOT_SIZE, 1);
    const tickSize = this.parseNumber(row.TICK_SIZE, 0.01);
    const strikePrice = this.parseNumber(row.STRIKE_PRICE);

    // Parse expiry date
    let expiryDate: Date | undefined;
    if (row.SM_EXPIRY_DATE && row.SM_EXPIRY_DATE !== '-0.01000') {
      expiryDate = this.parseDate(row.SM_EXPIRY_DATE);
    }

    // Determine option type
    let optionType: 'CE' | 'PE' | 'XX' | undefined;
    if (row.OPTION_TYPE && ['CE', 'PE', 'XX'].includes(row.OPTION_TYPE)) {
      optionType = row.OPTION_TYPE as 'CE' | 'PE' | 'XX';
    }

    return {
      securityId: row.SECURITY_ID,
      symbol: row.SYMBOL_NAME,
      displayName: row.DISPLAY_NAME || row.SYMBOL_NAME,
      exchange,
      segment: row.SEGMENT || 'C',
      instrumentType: row.INSTRUMENT || 'EQUITY',
      isin: row.ISIN !== 'NA' ? row.ISIN : undefined,
      lotSize,
      tickSize,
      underlyingSymbol: row.UNDERLYING_SYMBOL !== 'NA' ? row.UNDERLYING_SYMBOL : undefined,
      expiryDate,
      strikePrice,
      optionType,
      isActive: true,
      exchangeCode,
    };
  }

  /**
   * Get exchange segment from exchange ID and segment
   */
  private getExchangeSegment(exchId: string, segment: string): string {
    const exchange = exchId.toUpperCase();
    const seg = segment.toUpperCase();

    if (exchange === 'NSE') {
      if (seg === 'D') return 'NSE_FNO'; // Derivatives segment
      return seg === 'F' || seg === 'O' ? 'NSE_FNO' : 'NSE_EQ';
    } else if (exchange === 'BSE') {
      return seg === 'F' || seg === 'O' ? 'BSE_FNO' : 'BSE_EQ';
    } else if (exchange === 'MCX') {
      return 'MCX_COMM';
    }

    return 'NSE_EQ'; // Default
  }

  /**
   * Parse number from string
   */
  private parseNumber(value: string, defaultValue?: number): number {
    if (!value || value === 'NA' || value === '-0.01000') {
      return defaultValue || 0;
    }
    const parsed = parseFloat(value);
    return isNaN(parsed) ? (defaultValue || 0) : parsed;
  }

  /**
   * Parse date from string
   */
  private parseDate(dateStr: string): Date | undefined {
    try {
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? undefined : date;
    } catch {
      return undefined;
    }
  }

  /**
   * Get instruments with filtering
   */
  async getInstruments(filter?: InstrumentFilter): Promise<SearchResult> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    let filtered = [...this.instruments];

    if (filter) {
      filtered = this.applyFilter(filtered, filter);
    }

    return {
      instruments: filtered,
      total: filtered.length,
      page: 1,
      pageSize: filtered.length,
      hasMore: false,
    };
  }

  /**
   * Apply filters to instruments
   */
  private applyFilter(instruments: Instrument[], filter: InstrumentFilter): Instrument[] {
    let filtered = instruments;

    // Filter by exchange
    if (filter.exchange && filter.exchange.length > 0) {
      filtered = filtered.filter(inst => filter.exchange!.includes(inst.exchange));
    }

    // Filter by instrument type
    if (filter.instrumentType && filter.instrumentType.length > 0) {
      filtered = filtered.filter(inst => filter.instrumentType!.includes(inst.instrumentType));
    }

    // Filter by segment
    if (filter.segment && filter.segment.length > 0) {
      filtered = filtered.filter(inst => filter.segment!.includes(inst.segment));
    }

    // Filter by active status
    if (filter.isActive !== undefined) {
      filtered = filtered.filter(inst => inst.isActive === filter.isActive);
    }

    // Filter by expiry
    if (filter.hasExpiry !== undefined) {
      filtered = filtered.filter(inst => !!inst.expiryDate === filter.hasExpiry);
    }

    // Filter by lot size range
    if (filter.minLotSize !== undefined) {
      filtered = filtered.filter(inst => inst.lotSize >= filter.minLotSize!);
    }
    if (filter.maxLotSize !== undefined) {
      filtered = filtered.filter(inst => inst.lotSize <= filter.maxLotSize!);
    }

    // Search filter
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(inst =>
        inst.symbol.toLowerCase().includes(searchTerm) ||
        inst.displayName.toLowerCase().includes(searchTerm) ||
        (inst.isin && inst.isin.toLowerCase().includes(searchTerm)) ||
        (inst.underlyingSymbol && inst.underlyingSymbol.toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  }

  /**
   * Search instruments by query
   */
  async searchInstruments(query: string, limit: number = 50): Promise<Instrument[]> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    const searchTerm = query.toLowerCase();
    const results = this.instruments
      .filter(inst =>
        inst.symbol.toLowerCase().includes(searchTerm) ||
        inst.displayName.toLowerCase().includes(searchTerm) ||
        (inst.isin && inst.isin.toLowerCase().includes(searchTerm))
      )
      .slice(0, limit);

    return results;
  }

  /**
   * Get instrument by security ID
   */
  async getInstrumentById(securityId: string): Promise<Instrument | null> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    return this.instruments.find(inst => inst.securityId === securityId) || null;
  }

  /**
   * Get all exchanges
   */
  async getExchanges(): Promise<string[]> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    const exchanges = [...new Set(this.instruments.map(inst => inst.exchange))];
    return exchanges.sort();
  }

  /**
   * Get all instrument types
   */
  async getInstrumentTypes(): Promise<string[]> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    const types = [...new Set(this.instruments.map(inst => inst.instrumentType))];
    return types.sort();
  }

  /**
   * Get NSE derivatives and index instruments for subscription
   * Filters: EXCH_ID = NSE, SEGMENT = D, INSTRUMENT = FUTIDX, OPTIDX, INDEX
   */
  async getNSEDerivativesAndIndex(): Promise<Instrument[]> {
    if (this.instruments.length === 0) {
      await this.loadInstruments();
    }

    const targetInstruments = this.instruments.filter(inst => {
      return inst.exchange === 'NSE_FNO' &&
             inst.segment === 'D' &&
             ['FUTIDX', 'OPTIDX', 'INDEX'].includes(inst.instrumentType);
    });

    console.log(`🎯 Found ${targetInstruments.length} NSE derivatives and index instruments for subscription`);

    // Log breakdown by instrument type
    const breakdown: Record<string, number> = {};
    targetInstruments.forEach(inst => {
      breakdown[inst.instrumentType] = (breakdown[inst.instrumentType] || 0) + 1;
    });

    console.log('📊 Breakdown by instrument type:');
    Object.entries(breakdown).forEach(([type, count]) => {
      console.log(`   ${type}: ${count}`);
    });

    return targetInstruments;
  }

  /**
   * Get security IDs for NSE derivatives and index instruments
   */
  async getNSEDerivativesSecurityIds(): Promise<string[]> {
    const instruments = await this.getNSEDerivativesAndIndex();
    return instruments.map(inst => inst.securityId);
  }

  /**
   * Refresh cache
   */
  async refreshCache(): Promise<void> {
    this.cache.clear();
    this.lastLoadTime = 0;
    await this.loadInstruments();
  }

  /**
   * Cache management
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  private setCache<T>(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.cacheTTL,
    });
  }

  /**
   * Log instrument statistics
   */
  private logInstrumentStats(): void {
    const stats = {
      total: this.instruments.length,
      byExchange: {} as Record<string, number>,
      byInstrumentType: {} as Record<string, number>,
    };

    this.instruments.forEach(inst => {
      stats.byExchange[inst.exchange] = (stats.byExchange[inst.exchange] || 0) + 1;
      stats.byInstrumentType[inst.instrumentType] = (stats.byInstrumentType[inst.instrumentType] || 0) + 1;
    });

    console.log('📊 Instrument Statistics:');
    console.log(`   Total: ${stats.total}`);
    console.log('   By Exchange:', stats.byExchange);
    console.log('   By Type:', stats.byInstrumentType);
  }
}

// Export singleton instance
export const csvService = new CSVService();
