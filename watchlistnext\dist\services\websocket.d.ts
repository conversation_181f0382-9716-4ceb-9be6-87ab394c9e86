interface WebSocketMessage {
    type: string;
    data: any;
}
declare class WebSocketService {
    private static instance;
    private ws;
    private reconnectAttempts;
    private heartbeatInterval;
    private messageQueue;
    private isConnected;
    private messageHandlers;
    private constructor();
    static getInstance(): WebSocketService;
    connect(url: string): Promise<void>;
    send(message: WebSocketMessage): void;
    subscribe(type: string, handler: (data: any) => void): void;
    unsubscribe(type: string, handler: (data: any) => void): void;
    private handleMessage;
    private handleDisconnect;
    private handleError;
    private startHeartbeat;
    private stopHeartbeat;
    private processMessageQueue;
    close(): void;
    isConnectedToServer(): boolean;
}
export default WebSocketService;
//# sourceMappingURL=websocket.d.ts.map