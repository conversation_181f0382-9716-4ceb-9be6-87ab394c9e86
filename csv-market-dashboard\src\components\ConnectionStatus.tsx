"use client";

import React from 'react';

interface ConnectionStatusProps {
  connected: boolean;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ connected }) => {
  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <div
          className={`w-3 h-3 rounded-full ${
            connected ? 'bg-green-500 animate-pulse' : 'bg-red-500'
          }`}
        />
        <span
          className={`text-sm font-medium ${
            connected ? 'text-green-700' : 'text-red-700'
          }`}
        >
          {connected ? 'Connected' : 'Disconnected'}
        </span>
      </div>
      
      <div
        className={`px-3 py-1 rounded-full text-xs font-semibold ${
          connected
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}
      >
        {connected ? 'LIVE' : 'OFFLINE'}
      </div>
    </div>
  );
};

export default ConnectionStatus;
