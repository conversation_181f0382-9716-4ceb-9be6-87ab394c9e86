// ============================================================================
// LOGGER SERVICE - Structured Logging with Performance Optimization
// ============================================================================

import { Logger, LogEntry } from "@/types";

export class LoggerService implements Logger {
  private logLevel: "DEBUG" | "INFO" | "WARN" | "ERROR";
  private enableConsole: boolean;
  private enableFile: boolean;
  private logBuffer: LogEntry[];
  private maxBufferSize: number;
  private flushInterval: NodeJS.Timeout | null;

  constructor(
    logLevel: "DEBUG" | "INFO" | "WARN" | "ERROR" = "INFO",
    enableConsole: boolean = true,
    enableFile: boolean = false,
    maxBufferSize: number = 1000
  ) {
    this.logLevel = logLevel;
    this.enableConsole = enableConsole;
    this.enableFile = enableFile;
    this.logBuffer = [];
    this.maxBufferSize = maxBufferSize;
    this.flushInterval = null;

    if (this.enableFile) {
      this.startFlushInterval();
    }
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: Record<string, any>): void {
    if (this.shouldLog("DEBUG")) {
      this.logInternal("DEBUG", message, context);
    }
  }

  /**
   * Log info message
   */
  info(message: string, context?: Record<string, any>): void {
    if (this.shouldLog("INFO")) {
      this.logInternal("INFO", message, context);
    }
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: Record<string, any>): void {
    if (this.shouldLog("WARN")) {
      this.logInternal("WARN", message, context);
    }
  }

  /**
   * Log error message
   */
  error(message: string, context?: Record<string, any>): void {
    if (this.shouldLog("ERROR")) {
      this.logInternal("ERROR", message, context);
    }
  }

  /**
   * Core internal logging method
   */
  private logInternal(
    level: "DEBUG" | "INFO" | "WARN" | "ERROR",
    message: string,
    context?: Record<string, any>
  ): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      ...(context && { context: context as Record<string, unknown> }),
    };

    // Console logging
    if (this.enableConsole) {
      this.logToConsole(logEntry);
    }

    // File logging (buffered)
    if (this.enableFile) {
      this.addToBuffer(logEntry);
    }
  }

  /**
   * Log to console with colors
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const contextStr = entry.context ? ` ${JSON.stringify(entry.context)}` : "";

    const colors = {
      DEBUG: "\x1b[36m", // Cyan
      INFO: "\x1b[32m", // Green
      WARN: "\x1b[33m", // Yellow
      ERROR: "\x1b[31m", // Red
      RESET: "\x1b[0m", // Reset
    };

    const coloredLevel = `${colors[entry.level]}${entry.level}${colors.RESET}`;
    const logMessage = `[${timestamp}] ${coloredLevel}: ${entry.message}${contextStr}`;

    switch (entry.level) {
      case "DEBUG":
        console.debug(logMessage);
        break;
      case "INFO":
        console.info(logMessage);
        break;
      case "WARN":
        console.warn(logMessage);
        break;
      case "ERROR":
        console.error(logMessage);
        break;
    }
  }

  /**
   * Add log entry to buffer
   */
  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);

    // Flush if buffer is full
    if (this.logBuffer.length >= this.maxBufferSize) {
      this.flushBuffer();
    }
  }

  /**
   * Check if message should be logged based on level
   */
  private shouldLog(level: "DEBUG" | "INFO" | "WARN" | "ERROR"): boolean {
    const levels = ["DEBUG", "INFO", "WARN", "ERROR"];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex >= currentLevelIndex;
  }

  /**
   * Start periodic buffer flushing
   */
  private startFlushInterval(): void {
    this.flushInterval = setInterval(() => {
      this.flushBuffer();
    }, 5000); // Flush every 5 seconds
  }

  /**
   * Flush log buffer to file
   */
  private flushBuffer(): void {
    if (this.logBuffer.length === 0) {
      return;
    }

    // In a real implementation, you would write to a file here
    // For now, we'll just clear the buffer
    this.logBuffer = [];
  }

  /**
   * Set log level
   */
  setLogLevel(level: "DEBUG" | "INFO" | "WARN" | "ERROR"): void {
    this.logLevel = level;
  }

  /**
   * Get current log level
   */
  getLogLevel(): "DEBUG" | "INFO" | "WARN" | "ERROR" {
    return this.logLevel;
  }

  /**
   * Enable/disable console logging
   */
  setConsoleLogging(enabled: boolean): void {
    this.enableConsole = enabled;
  }

  /**
   * Enable/disable file logging
   */
  setFileLogging(enabled: boolean): void {
    this.enableFile = enabled;

    if (enabled && !this.flushInterval) {
      this.startFlushInterval();
    } else if (!enabled && this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
  }

  /**
   * Get buffer statistics
   */
  getBufferStats(): {
    size: number;
    maxSize: number;
    utilization: number;
  } {
    return {
      size: this.logBuffer.length,
      maxSize: this.maxBufferSize,
      utilization: (this.logBuffer.length / this.maxBufferSize) * 100,
    };
  }

  /**
   * Force flush buffer
   */
  flush(): void {
    this.flushBuffer();
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    this.flushBuffer();
  }

  /**
   * Create child logger with context
   */
  child(context: Record<string, any>): Logger {
    return {
      debug: (message: string, additionalContext?: Record<string, any>) =>
        this.debug(message, { ...context, ...additionalContext }),
      info: (message: string, additionalContext?: Record<string, any>) =>
        this.info(message, { ...context, ...additionalContext }),
      warn: (message: string, additionalContext?: Record<string, any>) =>
        this.warn(message, { ...context, ...additionalContext }),
      error: (message: string, additionalContext?: Record<string, any>) =>
        this.error(message, { ...context, ...additionalContext }),
    };
  }

  /**
   * Log performance metrics
   */
  performance(
    operation: string,
    duration: number,
    context?: Record<string, any>
  ): void {
    this.info(`Performance: ${operation} completed in ${duration}ms`, {
      operation,
      duration,
      ...context,
    });
  }

  /**
   * Log with custom level (public interface)
   */
  log(
    level: "DEBUG" | "INFO" | "WARN" | "ERROR",
    message: string,
    context?: Record<string, any>
  ): void {
    if (this.shouldLog(level)) {
      this.logInternal(level, message, context);
    }
  }
}

// Singleton instance
export const logger = new LoggerService(
  process.env.NODE_ENV === "development" ? "DEBUG" : "INFO",
  true, // Console logging enabled
  false, // File logging disabled for now
  1000 // Buffer size
);

export default LoggerService;
