"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { createWebSocketConnection, removeWebSocketListeners } from '@/utils/websocket';

interface IndexData {
  securityId: string;
  ticker: string;
  name: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  hasLiveData: boolean;
}

const KeyIndicesComponent: React.FC = () => {
  const [marketData, setMarketData] = useState<{ [key: string]: IndexData }>({});
  const [selectedExchange, setSelectedExchange] = useState<'BSE' | 'NSE' | 'ALL'>('ALL');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initial fetch to get all indices
    const fetchInitialData = async () => {
      try {
        const response = await fetch('/api/indices?limit=500');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        
        // Convert array to map for easier updates
        const dataMap: { [key: string]: IndexData } = {};
        data.indices.forEach((index: IndexData) => {
          dataMap[index.securityId] = index;
        });
        
        setMarketData(dataMap);
        setIsConnected(data.connected);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching indices data:', error);
        setIsLoading(false);
      }
    };

    fetchInitialData();

    // Setup WebSocket connection for real-time updates
    const socket = createWebSocketConnection({
      onConnect: () => setIsConnected(true),
      onDisconnect: () => setIsConnected(false),
      onMarketData: (data) => {
        setMarketData(prev => ({
          ...prev,
          [data.securityId]: {
            ...prev[data.securityId],
            ...data,
            hasLiveData: true
          }
        }));
      }
    });

    // Cleanup
    return () => {
      removeWebSocketListeners();
    };
  }, []);

  const getFilteredIndices = useMemo(() => {
    const indices = Object.values(marketData);
    
    if (selectedExchange === 'ALL') {
      return indices;
    }
    return indices.filter(index => index.exchange.startsWith(selectedExchange));
  }, [marketData, selectedExchange]);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num === 0) return '0.00';
    return num.toFixed(decimals);
  };

  const formatVolume = (volume: number): string => {
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const getChangeColor = (change: number): string => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeBgColor = (change: number): string => {
    if (change > 0) return 'bg-green-50 border-green-200';
    if (change < 0) return 'bg-red-50 border-red-200';
    return 'bg-gray-50 border-gray-200';
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <h2 className="text-2xl font-bold text-gray-800">Key Indices</h2>
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span>{isConnected ? 'Live' : 'Disconnected'}</span>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {['ALL', 'BSE', 'NSE'].map((exchange) => (
            <button
              key={exchange}
              onClick={() => setSelectedExchange(exchange as 'BSE' | 'NSE' | 'ALL')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedExchange === exchange
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {exchange}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {getFilteredIndices.map((index, i) => (
          <motion.div
            key={index.securityId}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.05 }}
            className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${getChangeBgColor(index.change)}`}
          >
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-bold text-lg text-gray-800">{index.ticker}</h3>
                <p className="text-sm text-gray-600 truncate" title={index.name}>
                  {index.name}
                </p>
              </div>
              <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                {index.exchange.replace('_EQ', '')}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-2xl font-bold text-gray-800">
                  {formatNumber(index.ltp)}
                </span>
                <div className={`text-right ${getChangeColor(index.change)}`}>
                  <div className="font-semibold">
                    {index.change !== 0 ? (index.change > 0 ? '+' : '') + formatNumber(index.change) : '0.00'}
                  </div>
                  <div className="text-sm">
                    ({index.changePercent !== 0 ? (index.changePercent > 0 ? '+' : '') + formatNumber(index.changePercent) : '0.00'}%)
                  </div>
                </div>
              </div>

              <div className="flex justify-between text-sm text-gray-600">
                <div>
                  <span className="block">High: {formatNumber(index.high)}</span>
                  <span className="block">Low: {formatNumber(index.low)}</span>
                </div>
                <div className="text-right">
                  <span className="block">Vol: {formatVolume(index.volume)}</span>
                  <span className="block">ID: {index.securityId}</span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {getFilteredIndices.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No indices found for {selectedExchange}</div>
          <div className="text-gray-400 text-sm mt-2">
            Try selecting a different exchange or check your connection
          </div>
        </div>
      )}

      {isLoading && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">Loading market data...</div>
          <div className="text-gray-400 text-sm mt-2">
            Connecting to market feed
          </div>
        </div>
      )}

      <div className="mt-6 text-center text-sm text-gray-500">
        Showing {getFilteredIndices.length} indices • Real-time updates
        {Object.keys(marketData).length > 0 && (
          <span className="ml-2">• {Object.values(marketData).filter(i => i.hasLiveData).length} live indices</span>
        )}
      </div>
    </div>
  );
};

export default KeyIndicesComponent;
