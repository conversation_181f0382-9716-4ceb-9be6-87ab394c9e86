"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
class Logger {
    constructor(options = {}) {
        this.silent = options.silent || false;
        // Only enable console logging in development and for errors
        this.enableConsole = options.enableConsole ?? (process.env.NODE_ENV === 'development');
    }
    log(level, message, ...args) {
        if (this.silent)
            return;
        // Only log errors and market summaries to console
        if (this.enableConsole && (level === 'error' || message.includes('Market Data Summary'))) {
            const timestamp = new Date().toISOString();
            const prefix = `[${timestamp}] ${level.toUpperCase()}:`;
            if (level === 'error') {
                console.error(prefix, message, ...args);
            }
            else {
                console.log(prefix, message, ...args);
            }
        }
    }
    debug(message, ...args) {
        // Debug messages are completely silent unless explicitly enabled
        if (process.env.DEBUG_LOGS === 'true') {
            this.log('debug', message, ...args);
        }
    }
    info(message, ...args) {
        // Only log market summaries and critical info
        if (message.includes('Market Data Summary') || process.env.DEBUG_LOGS === 'true') {
            this.log('info', message, ...args);
        }
    }
    warn(message, ...args) {
        // Only log critical warnings
        if (message.includes('error') || message.includes('fail') || message.includes('disconnect')) {
            this.log('warn', message, ...args);
        }
    }
    error(message, ...args) {
        this.log('error', message, ...args);
    }
}
exports.logger = new Logger({
    silent: process.env.NODE_ENV === 'test',
    enableConsole: process.env.NODE_ENV === 'development'
});
//# sourceMappingURL=logger.js.map