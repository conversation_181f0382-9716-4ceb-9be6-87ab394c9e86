"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  BarChart3,
  TrendingUp,
  Building2,
  Factory,
  Layers,
  Package,
  PieChart,
  Menu,
  X,
  Activity,
  Zap,
  Sun,
  Moon,
} from "lucide-react";

const Navigation: React.FC = () => {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    {
      href: "/",
      label: "Dashboard",
      icon: BarChart3,
      description: "Market Overview",
    },
    {
      href: "/simple-dashboard",
      label: "Simple",
      icon: Activity,
      description: "Fast Dashboard",
    },
    {
      href: "/simple-sectors",
      label: "Simple Sectors",
      icon: Building2,
      description: "Quick Sectors",
    },
    {
      href: "/stocks",
      label: "Stocks",
      icon: TrendingUp,
      description: "Live Stock Data",
    },
    {
      href: "/sectors",
      label: "Sectors",
      icon: Building2,
      description: "Sector Analysis",
    },
    {
      href: "/industries",
      label: "Industries",
      icon: Factory,
      description: "Industry Analysis",
    },
    {
      href: "/sub-sectors",
      label: "Sub-Sectors",
      icon: Layers,
      description: "Sub-Sector Analysis",
    },
    {
      href: "/micro-categories",
      label: "Micro-Categories",
      icon: Package,
      description: "Micro-Category Analysis",
    },
    {
      href: "/index-analysis",
      label: "Indices",
      icon: PieChart,
      description: "Index Analytics",
    },
  ];

  return (
    <>
      {/* Enhanced Navigation Header */}
      <nav
        className={`sticky top-0 z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50"
            : "bg-white shadow-sm border-b border-gray-200"
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo/Brand */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-trading rounded-xl shadow-lg">
                <Activity className="w-6 h-6 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold gradient-text">Dhan Market</h1>
                <p className="text-xs text-gray-500 -mt-1">
                  Real-time Trading Dashboard
                </p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`group relative flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? "bg-primary-50 text-primary-600 shadow-sm"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    <Icon
                      className={`w-4 h-4 transition-colors ${
                        isActive
                          ? "text-primary-600"
                          : "text-gray-400 group-hover:text-gray-600"
                      }`}
                    />
                    <span>{item.label}</span>

                    {/* Active indicator */}
                    {isActive && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full" />
                    )}
                  </Link>
                );
              })}
            </div>

            {/* Live Status Indicator */}
            <div className="hidden lg:flex items-center space-x-3">
              <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-50 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-xs font-medium text-green-700">Live</span>
              </div>
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Zap className="w-3 h-3" />
                <span>Real-time</span>
              </div>
              {/* Theme Toggle */}
              <button
                aria-label="Toggle Dark Mode"
                className="ml-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                onClick={() => {
                  if (document.documentElement.classList.contains("dark")) {
                    document.documentElement.classList.remove("dark");
                    localStorage.setItem("theme", "light");
                  } else {
                    document.documentElement.classList.add("dark");
                    localStorage.setItem("theme", "dark");
                  }
                }}
              >
                {typeof window !== "undefined" &&
                document.documentElement.classList.contains("dark") ? (
                  <Sun className="w-5 h-5 text-yellow-400" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-700" />
                )}
              </button>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-4 py-3 space-y-1">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors ${
                      isActive
                        ? "bg-primary-50 text-primary-600"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    <Icon
                      className={`w-5 h-5 ${
                        isActive ? "text-primary-600" : "text-gray-400"
                      }`}
                    />
                    <div className="flex-1">
                      <div className="font-medium">{item.label}</div>
                      <div className="text-xs text-gray-500">
                        {item.description}
                      </div>
                    </div>
                  </Link>
                );
              })}

              {/* Mobile Live Status */}
              <div className="flex items-center justify-between pt-3 mt-3 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-sm font-medium text-green-700">
                    Live Market Data
                  </span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Zap className="w-3 h-3" />
                  <span>Real-time</span>
                </div>
                {/* Theme Toggle for Mobile */}
                <button
                  aria-label="Toggle Dark Mode"
                  className="ml-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                  onClick={() => {
                    if (document.documentElement.classList.contains("dark")) {
                      document.documentElement.classList.remove("dark");
                      localStorage.setItem("theme", "light");
                    } else {
                      document.documentElement.classList.add("dark");
                      localStorage.setItem("theme", "dark");
                    }
                  }}
                >
                  {typeof window !== "undefined" &&
                  document.documentElement.classList.contains("dark") ? (
                    <Sun className="w-5 h-5 text-yellow-400" />
                  ) : (
                    <Moon className="w-5 h-5 text-gray-700" />
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  );
};

export default Navigation;
