const { Client } = require("pg");
require("dotenv").config();

// Database connection
const client = new Client({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false,
  },
});

async function testWebSocketInstruments() {
  try {
    console.log("🔌 Connecting to database...");
    await client.connect();
    console.log("✅ Connected to database");

    // Test 1: Get Bank Nifty constituents
    console.log("\n📊 Test 1: Bank Nifty Constituents");
    const bankSymbols = [
      "HDFCBANK",
      "ICICIBANK",
      "AXISBANK",
      "SBIN",
      "KOTAKBANK",
    ];

    for (const symbol of bankSymbols) {
      const query =
        "SELECT company_name, symbol, nse_security_id, bse_security_id, sector_name FROM company_list WHERE symbol = $1";
      const result = await client.query(query, [symbol]);

      if (result.rows.length > 0) {
        const company = result.rows[0];
        console.log(`✅ ${symbol}:`);
        console.log(`   Company: ${company.company_name}`);
        console.log(
          `   NSE Security ID: ${company.nse_security_id} (Use with NSE_EQ)`
        );
        console.log(
          `   BSE Security ID: ${company.bse_security_id} (Use with BSE_EQ)`
        );
        console.log(`   Sector: ${company.sector_name}`);

        // Generate WebSocket format
        if (company.nse_security_id) {
          console.log(
            `   🔗 NSE WebSocket: { "ExchangeSegment": "NSE_EQ", "SecurityId": "${company.nse_security_id}" }`
          );
        }
        if (company.bse_security_id) {
          console.log(
            `   🔗 BSE WebSocket: { "ExchangeSegment": "BSE_EQ", "SecurityId": "${company.bse_security_id}" }`
          );
        }
        console.log("");
      } else {
        console.log(`❌ ${symbol}: Not found in database`);
      }
    }

    // Test 2: Get Financial Services sector instruments
    console.log("\n📊 Test 2: Financial Services Sector (First 10)");
    const sectorQuery = `
      SELECT company_name, symbol, nse_security_id, bse_security_id
      FROM company_list
      WHERE sector_name = 'Financial Services'
      AND (nse_security_id IS NOT NULL OR bse_security_id IS NOT NULL)
      LIMIT 10
    `;
    const sectorResult = await client.query(sectorQuery);

    console.log(
      `Found ${sectorResult.rows.length} Financial Services companies:`
    );
    sectorResult.rows.forEach((company, index) => {
      console.log(`${index + 1}. ${company.company_name} (${company.symbol})`);
      if (company.nse_security_id) {
        console.log(
          `   NSE: { "ExchangeSegment": "NSE_EQ", "SecurityId": "${company.nse_security_id}" }`
        );
      }
      if (company.bse_security_id) {
        console.log(
          `   BSE: { "ExchangeSegment": "BSE_EQ", "SecurityId": "${company.bse_security_id}" }`
        );
      }
    });

    // Test 3: Generate subscription message for Bank Nifty
    console.log("\n📊 Test 3: Bank Nifty WebSocket Subscription Message");
    const bankNiftyQuery = `
      SELECT symbol, nse_security_id
      FROM company_list
      WHERE symbol IN ('HDFCBANK', 'ICICIBANK', 'AXISBANK', 'SBIN', 'KOTAKBANK', 'INDUSINDBK', 'FEDERALBNK', 'BANKBARODA', 'IDFCFIRSTB', 'AUBANK')
      AND nse_security_id IS NOT NULL
    `;
    const bankNiftyResult = await client.query(bankNiftyQuery);

    const instrumentList = bankNiftyResult.rows.map((row) => ({
      ExchangeSegment: "NSE_EQ",
      SecurityId: row.nse_security_id,
    }));

    const subscriptionMessage = {
      RequestCode: 17, // Quote data
      InstrumentCount: instrumentList.length,
      InstrumentList: instrumentList,
    };

    console.log("Bank Nifty WebSocket Subscription Message:");
    console.log(JSON.stringify(subscriptionMessage, null, 2));

    // Test 4: Count instruments by exchange
    console.log("\n📊 Test 4: Instrument Counts by Exchange");
    const countQueries = [
      "SELECT COUNT(*) as count FROM company_list WHERE nse_security_id IS NOT NULL",
      "SELECT COUNT(*) as count FROM company_list WHERE bse_security_id IS NOT NULL",
      "SELECT COUNT(*) as count FROM company_list WHERE nse_security_id IS NOT NULL AND bse_security_id IS NOT NULL",
    ];

    const [nseCount, bseCount, bothCount] = await Promise.all(
      countQueries.map((query) => client.query(query))
    );

    console.log(`📈 NSE instruments available: ${nseCount.rows[0].count}`);
    console.log(`📈 BSE instruments available: ${bseCount.rows[0].count}`);
    console.log(`📈 Companies with both NSE & BSE: ${bothCount.rows[0].count}`);

    // Test 5: Sector distribution for WebSocket planning
    console.log("\n📊 Test 5: Top Sectors for WebSocket Subscriptions");
    const sectorDistQuery = `
      SELECT 
        sector_name,
        COUNT(*) as total_companies,
        COUNT(CASE WHEN nse_security_id IS NOT NULL THEN 1 END) as nse_instruments,
        COUNT(CASE WHEN bse_security_id IS NOT NULL THEN 1 END) as bse_instruments
      FROM company_list
      WHERE sector_name IS NOT NULL
      GROUP BY sector_name
      ORDER BY total_companies DESC
      LIMIT 10
    `;
    const sectorDistResult = await client.query(sectorDistQuery);

    console.log("Top sectors by instrument count:");
    sectorDistResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.sector_name}`);
      console.log(
        `   Total: ${row.total_companies}, NSE: ${row.nse_instruments}, BSE: ${row.bse_instruments}`
      );
    });

    console.log("\n🎉 WebSocket instrument testing completed!");
    console.log("\n💡 Usage Tips:");
    console.log('- Use NSE_SECURITY_ID with ExchangeSegment: "NSE_EQ"');
    console.log('- Use BSE_SECURITY_ID with ExchangeSegment: "BSE_EQ"');
    console.log("- Maximum 5000 instruments per WebSocket connection");
    console.log("- Maximum 5 WebSocket connections per user");
    console.log("- Total capacity: 25,000 instruments across all connections");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  } finally {
    await client.end();
    console.log("🔌 Database connection closed");
  }
}

// Run the test
if (require.main === module) {
  testWebSocketInstruments();
}

module.exports = { testWebSocketInstruments };
