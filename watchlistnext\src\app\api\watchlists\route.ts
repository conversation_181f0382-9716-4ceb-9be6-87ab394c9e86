import { NextResponse } from "next/server";
import { DatabaseService } from "@/services/database";

const db = DatabaseService.getInstance();

export async function GET() {
  try {
    const watchlists = await db.getWatchlists();
    return NextResponse.json({ success: true, data: watchlists });
  } catch (error) {
    console.error("Error fetching watchlists:", error);
    return new Response(
      JSON.stringify({ success: false, error: "Failed to fetch watchlists" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { name, type } = await request.json();

    if (!name || !type) {
      return new Response(
        JSON.stringify({ success: false, error: "Name and type are required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    const watchlistId = await db.createWatchlist(name, type);
    return NextResponse.json({
      success: true,
      data: { id: watchlistId, name, type },
    });
  } catch (error) {
    console.error("Error creating watchlist:", error);
    return new Response(
      JSON.stringify({ success: false, error: "Failed to create watchlist" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return new Response(
        JSON.stringify({ success: false, error: "Watchlist ID is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    await db.deleteWatchlist(parseInt(id));
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting watchlist:", error);
    return new Response(
      JSON.stringify({ success: false, error: "Failed to delete watchlist" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
