"use client";

import React, { useState } from 'react';
import { FilterPanelProps, InstrumentFilter } from '@/types';

const FilterPanel: React.FC<FilterPanelProps> = ({
  filter,
  onFilterChange,
  exchanges,
  instrumentTypes,
  segments,
}) => {
  const [searchTerm, setSearchTerm] = useState(filter.search || '');

  const handleExchangeChange = (exchange: string, checked: boolean) => {
    const currentExchanges = filter.exchange || [];
    const newExchanges = checked
      ? [...currentExchanges, exchange]
      : currentExchanges.filter(e => e !== exchange);
    
    onFilterChange({
      ...filter,
      exchange: newExchanges.length > 0 ? newExchanges : undefined,
    });
  };

  const handleInstrumentTypeChange = (type: string, checked: boolean) => {
    const currentTypes = filter.instrumentType || [];
    const newTypes = checked
      ? [...currentTypes, type]
      : currentTypes.filter(t => t !== type);
    
    onFilterChange({
      ...filter,
      instrumentType: newTypes.length > 0 ? newTypes : undefined,
    });
  };

  const handleSegmentChange = (segment: string, checked: boolean) => {
    const currentSegments = filter.segment || [];
    const newSegments = checked
      ? [...currentSegments, segment]
      : currentSegments.filter(s => s !== segment);
    
    onFilterChange({
      ...filter,
      segment: newSegments.length > 0 ? newSegments : undefined,
    });
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onFilterChange({
      ...filter,
      search: value || undefined,
    });
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    onFilterChange({});
  };

  const isFilterActive = () => {
    return !!(
      filter.exchange?.length ||
      filter.instrumentType?.length ||
      filter.segment?.length ||
      filter.search ||
      filter.isActive !== undefined ||
      filter.hasExpiry !== undefined
    );
  };

  return (
    <div className="filter-panel">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {isFilterActive() && (
          <button
            onClick={handleClearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Search */}
      <div className="filter-group">
        <label className="filter-label">Search</label>
        <input
          type="text"
          placeholder="Search by symbol, name, or ISIN..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="filter-input"
        />
      </div>

      {/* Exchanges */}
      <div className="filter-group">
        <label className="filter-label">Exchanges</label>
        <div className="space-y-2 max-h-32 overflow-y-auto custom-scrollbar">
          {exchanges.map((exchange) => (
            <label key={exchange} className="flex items-center">
              <input
                type="checkbox"
                checked={filter.exchange?.includes(exchange) || false}
                onChange={(e) => handleExchangeChange(exchange, e.target.checked)}
                className="filter-checkbox"
              />
              <span className="text-sm text-gray-700">{exchange}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Instrument Types */}
      <div className="filter-group">
        <label className="filter-label">Instrument Types</label>
        <div className="space-y-2 max-h-32 overflow-y-auto custom-scrollbar">
          {instrumentTypes.map((type) => (
            <label key={type} className="flex items-center">
              <input
                type="checkbox"
                checked={filter.instrumentType?.includes(type) || false}
                onChange={(e) => handleInstrumentTypeChange(type, e.target.checked)}
                className="filter-checkbox"
              />
              <span className="text-sm text-gray-700">{type}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Segments */}
      <div className="filter-group">
        <label className="filter-label">Segments</label>
        <div className="space-y-2">
          {segments.map((segment) => (
            <label key={segment} className="flex items-center">
              <input
                type="checkbox"
                checked={filter.segment?.includes(segment) || false}
                onChange={(e) => handleSegmentChange(segment, e.target.checked)}
                className="filter-checkbox"
              />
              <span className="text-sm text-gray-700">
                {segment === 'C' ? 'Cash (C)' : 
                 segment === 'F' ? 'Futures (F)' : 
                 segment === 'O' ? 'Options (O)' : segment}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Additional Filters */}
      <div className="filter-group">
        <label className="filter-label">Additional Filters</label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filter.isActive === true}
              onChange={(e) => onFilterChange({
                ...filter,
                isActive: e.target.checked ? true : undefined,
              })}
              className="filter-checkbox"
            />
            <span className="text-sm text-gray-700">Active Only</span>
          </label>
          
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filter.hasExpiry === true}
              onChange={(e) => onFilterChange({
                ...filter,
                hasExpiry: e.target.checked ? true : undefined,
              })}
              className="filter-checkbox"
            />
            <span className="text-sm text-gray-700">Has Expiry</span>
          </label>
        </div>
      </div>

      {/* Lot Size Range */}
      <div className="filter-group">
        <label className="filter-label">Lot Size Range</label>
        <div className="grid grid-cols-2 gap-2">
          <input
            type="number"
            placeholder="Min"
            value={filter.minLotSize || ''}
            onChange={(e) => onFilterChange({
              ...filter,
              minLotSize: e.target.value ? parseInt(e.target.value) : undefined,
            })}
            className="filter-input text-sm"
          />
          <input
            type="number"
            placeholder="Max"
            value={filter.maxLotSize || ''}
            onChange={(e) => onFilterChange({
              ...filter,
              maxLotSize: e.target.value ? parseInt(e.target.value) : undefined,
            })}
            className="filter-input text-sm"
          />
        </div>
      </div>

      {/* Filter Summary */}
      {isFilterActive() && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Active Filters:</h4>
          <div className="space-y-1 text-xs text-blue-700">
            {filter.exchange?.length && (
              <div>Exchanges: {filter.exchange.join(', ')}</div>
            )}
            {filter.instrumentType?.length && (
              <div>Types: {filter.instrumentType.join(', ')}</div>
            )}
            {filter.segment?.length && (
              <div>Segments: {filter.segment.join(', ')}</div>
            )}
            {filter.search && (
              <div>Search: "{filter.search}"</div>
            )}
            {filter.isActive && (
              <div>Active instruments only</div>
            )}
            {filter.hasExpiry && (
              <div>With expiry date</div>
            )}
            {(filter.minLotSize || filter.maxLotSize) && (
              <div>
                Lot size: {filter.minLotSize || 0} - {filter.maxLotSize || '∞'}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPanel;
