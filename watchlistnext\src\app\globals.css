@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42;
  --background-rgb: 248, 250, 252;
  --primary-rgb: 59, 130, 246;
  --success-rgb: 16, 185, 129;
  --danger-rgb: 239, 68, 68;
  --warning-rgb: 245, 158, 11;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family:
    "Inter",
    system-ui,
    -apple-system,
    sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced Selection */
::selection {
  background-color: rgba(59, 130, 246, 0.2);
  color: rgb(var(--foreground-rgb));
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid rgb(var(--primary-rgb));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Enhanced Animations */
@keyframes priceFlash {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(16, 185, 129, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

@keyframes priceFlashDown {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(239, 68, 68, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleInBounce {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Utility Classes */
.price-flash-up {
  animation: priceFlash 0.8s ease-in-out;
}

.price-flash-down {
  animation: priceFlashDown 0.8s ease-in-out;
}

.slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.slide-in-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.scale-in-bounce {
  animation: scaleInBounce 0.4s ease-out;
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Professional Trading UI Enhancements */
.trading-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.trading-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(59, 130, 246, 0.3);
}

.market-status-live {
  position: relative;
  overflow: hidden;
}

.market-status-live::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(16, 185, 129, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

.price-ticker {
  font-family: "JetBrains Mono", monospace;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

.sector-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.stock-row {
  transition: all 0.2s ease;
  border-radius: 0.75rem;
  padding: 0.75rem;
  margin: -0.75rem;
}

.stock-row:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateX(4px);
}

/* Enhanced Table Styling */
.trading-table {
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.trading-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.75rem;
  color: #475569;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.trading-table td {
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.trading-table tbody tr:hover td {
  background: rgba(59, 130, 246, 0.02);
}

/* Professional Header Styling */
.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-title {
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* Enhanced Status Indicators */
.status-indicator {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.live {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.status-indicator.live::before {
  content: "";
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #991b1b;
  border: 1px solid #fca5a5;
}

/* Modern Search Input */
.search-input {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem 0.75rem 3rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 1);
}

/* Professional Button Variants */
.btn-trading {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-trading:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-trading:active {
  transform: translateY(0);
}

/* Responsive Grid Enhancements */
@media (max-width: 768px) {
  .sector-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .trading-table {
    font-size: 0.875rem;
  }

  .trading-table th,
  .trading-table td {
    padding: 0.75rem 0.5rem;
  }
}
