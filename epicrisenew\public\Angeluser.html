<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Add Angel Client</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f4f4f9;
      }
      .form-container {
        max-width: 600px;
        margin: auto;
        background: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
      h1 {
        text-align: center;
        color: #333;
      }
      label {
        font-weight: bold;
        margin-top: 10px;
        display: block;
      }
      input,
      button {
        width: 100%;
        padding: 10px;
        margin-top: 5px;
        margin-bottom: 15px;
        border-radius: 5px;
        border: 1px solid #ccc;
        font-size: 16px;
      }
      button {
        background-color: #28a745;
        color: #fff;
        border: none;
        cursor: pointer;
      }
      button:hover {
        background-color: #218838;
      }
      .note {
        font-size: 0.9em;
        color: #555;
      }
      .message {
        margin-top: 20px;
        font-size: 1rem;
        text-align: center;
      }
      .success {
        color: green;
      }
      .error {
        color: red;
      }
    </style>
  </head>
  <body>
    <div class="form-container">
      <h1>Add Angel Client</h1>
      <form id="angelForm">
        <label for="userId">User ID</label>
        <input type="text" id="userId" name="userId" required />

        <label for="password">Password</label>
        <input type="password" id="password" name="password" required />

        <label for="apiKey">API Key</label>
        <input type="text" id="apiKey" name="apiKey" required />

        <label for="totpKey">TOTP Key</label>
        <input type="text" id="totpKey" name="totpKey" required />

        <label for="clientName">Client Name</label>
        <input type="text" id="clientName" name="clientName" required />

        <label for="email">Email</label>
        <input type="email" id="email" name="email" required />

        <label for="phoneNumber">Phone Number</label>
        <input type="text" id="phoneNumber" name="phoneNumber" required />

        <label for="jwtToken">JWT Token (optional)</label>
        <input type="text" id="jwtToken" name="jwtToken" />

        <label for="refreshToken">Refresh Token (optional)</label>
        <input type="text" id="refreshToken" name="refreshToken" />

        <label for="feedToken">Feed Token (optional)</label>
        <input type="text" id="feedToken" name="feedToken" />

        <label for="capital">Capital:</label>
        <input type="number" id="capital" name="capital" required /><br />

        <label for="state">State</label>
        <input
          type="text"
          id="state"
          name="state"
          value="live"
          readonly
          class="note"
        />
        <small class="note">Default state is set to "live"</small>

        <button type="submit">Add Client</button>
      </form>
      <div class="message" id="message"></div>
    </div>

    <script>
      const form = document.getElementById("angelForm");
      const messageDiv = document.getElementById("message");

      form.addEventListener("submit", async (event) => {
        event.preventDefault(); // Prevent the default form submission

        // Gather form data
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        try {
          // Send data to the backend
          const response = await fetch("/addAngeluser", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
          });

          const result = await response.json();

          if (response.ok) {
            // Display success message
            messageDiv.textContent = "User added successfully!";
            messageDiv.className = "message success";
            form.reset(); // Clear the form
          } else {
            // Display error message
            messageDiv.textContent = result.error || "Failed to add user.";
            messageDiv.className = "message error";
          }
        } catch (error) {
          // Display error message in case of network issues
          console.error("Error:", error);
          messageDiv.textContent =
            "An error occurred while submitting the form.";
          messageDiv.className = "message error";
        }
      });
    </script>
  </body>
</html>
