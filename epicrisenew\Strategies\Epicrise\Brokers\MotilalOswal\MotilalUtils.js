const getCredentials = require("../../../../cred");
const axios = require("axios");
const OrderResponse = require("../../../../models/OrderResponse");

const { BUY_ADJUSTMENT, SELL_ADJUSTMENT } = require("../../Utils/utilities");
// Helper functions for order placement
async function handleClientOrder(
  client,
  document,
  price,
  transactionType,
  credentials,
  stopLossPrice
) {
  const { clientName, authToken, apiKey, capital, userId } = client;

  if (!authToken || !apiKey || !capital) {
    return;
  }

  const adjustedPrice = adjustPriceForTransaction(price, transactionType);

  // Check if stopLossPrice is undefined or null
  if (stopLossPrice === undefined || stopLossPrice === null) {
    // Calculate default stop loss price (2.5% from message price)
    if (transactionType.toUpperCase() === "SELL") {
      // For SELL orders, stop loss is 2.5% above the message price
      stopLossPrice = price * 1.025;
    } else {
      // For BUY orders, stop loss is 2.5% below the message price
      stopLossPrice = price * 0.975;
    }
  }

  const quantity = Math.floor(capital / adjustedPrice);

  const orderData = createOrderData(
    document,
    transactionType,
    adjustedPrice,
    quantity
  );

  try {
    const response = await placeOrder(
      orderData,
      credentials,
      authToken,
      apiKey,
      userId
    );

    // Save primary order response to database
    try {
      const primaryOrderRecord = new OrderResponse({
        clientName: clientName,
        broker: "MOTILAL",
        symbol: document.symbol,
        transactionType: transactionType.toUpperCase(),
        orderType: "PRIMARY",
        price: adjustedPrice,
        quantity: quantity,
        status: response && response.status ? "SUCCESS" : "FAILED",
        response: response,
      });
      await primaryOrderRecord.save();
    } catch (dbError) {
      // Error saving primary order to database - silently handled
    }

    setTimeout(async () => {
      const stopLossOrderData = createStopLossOrderData(
        document,
        transactionType,
        price,
        stopLossPrice,
        quantity
      );

      try {
        const stopLossResponse = await placeOrder(
          stopLossOrderData,
          credentials,
          authToken,
          apiKey,
          userId
        );

        // Save stop-loss order response to database
        try {
          const stopLossOrderRecord = new OrderResponse({
            clientName: clientName,
            broker: "MOTILAL",
            symbol: document.symbol,
            transactionType: stopLossOrderData.buyorsell,
            orderType: "STOPLOSS",
            price: parseFloat(stopLossOrderData.price),
            quantity: quantity,
            status: stopLossResponse && stopLossResponse.status ? "SUCCESS" : "FAILED",
            response: stopLossResponse,
          });
          await stopLossOrderRecord.save();
        } catch (dbError) {
          // Error saving stop-loss order to database - silently handled
        }
      } catch (error) {
        // Save failed stop-loss order to database
        try {
          const failedStopLossRecord = new OrderResponse({
            clientName: clientName,
            broker: "MOTILAL",
            symbol: document.symbol,
            transactionType: stopLossOrderData.buyorsell,
            orderType: "STOPLOSS",
            price: parseFloat(stopLossOrderData.price),
            quantity: quantity,
            status: "FAILED",
            response: { error: error.message, orderData: stopLossOrderData },
          });
          await failedStopLossRecord.save();
        } catch (dbError) {
          // Error saving failed stop-loss order to database - silently handled
        }
      }
    }, 5000);
  } catch (error) {
    // Save failed primary order to database
    try {
      const failedPrimaryRecord = new OrderResponse({
        clientName: clientName,
        broker: "MOTILAL",
        symbol: document.symbol,
        transactionType: transactionType.toUpperCase(),
        orderType: "PRIMARY",
        price: adjustedPrice,
        quantity: quantity,
        status: "FAILED",
        response: { error: error.message, orderData: orderData },
      });
      await failedPrimaryRecord.save();
    } catch (dbError) {
      // Error saving failed primary order to database - silently handled
    }
  }
}

// Function to adjust price based on transaction type
function adjustPriceForTransaction(price, transactionType) {
  if (transactionType.toUpperCase() === "BUY") {
    const adjustedPrice = price * BUY_ADJUSTMENT;
    return adjustedPrice;
  } else if (transactionType.toUpperCase() === "SELL") {
    const adjustedPrice = price * SELL_ADJUSTMENT;
    return adjustedPrice;
  }

  throw new Error("Invalid transaction type");
}

// Function to create order data
function createOrderData(document, transactionType, price, quantity) {
  const token = Number(document.token);

  const orderData = {
    exchange: "NSE",
    symboltoken: token,
    buyorsell: transactionType.toUpperCase(),
    ordertype: "LIMIT",
    producttype: "VALUEPLUS",
    orderduration: "DAY",
    price: Number(roundToTwoDecimalsEndingInZero(price)),
    quantityinlot: quantity,
    amoorder: "N",
  };

  return orderData;
}

// Function to create stop-loss order data
function createStopLossOrderData(
  document,
  transactionType,
  messagePrice,
  stopLossPrice,
  quantity
) {
  // For stop loss, we need to reverse the transaction type
  const reversedTransactionType =
    transactionType.toUpperCase() === "BUY" ? "SELL" : "BUY";

  // Check if stopLossPrice is undefined or null
  if (stopLossPrice === undefined || stopLossPrice === null) {
    // Calculate default stop loss price (2.5% from message price)
    if (transactionType.toUpperCase() === "SELL") {
      // For SELL orders, stop loss is 2.5% above the message price
      stopLossPrice = messagePrice * 1.025;
    } else {
      // For BUY orders, stop loss is 2.5% below the message price
      stopLossPrice = messagePrice * 0.975;
    }
  }

  // For a SELL order, the stop loss is a BUY order with a trigger price above the current price
  // For a BUY order, the stop loss is a SELL order with a trigger price below the current price
  let triggerPrice;
  let stopLossOrderPrice;

  if (transactionType.toUpperCase() === "SELL") {
    // For SELL orders, the stop loss is a BUY order
    // The trigger price is the stop loss price from the message
    triggerPrice = stopLossPrice;
    // The stop loss order price should be 0.25% above the trigger price
    stopLossOrderPrice = triggerPrice * 1.0025;
  } else {
    // For BUY orders, the stop loss is a SELL order
    // The trigger price is the stop loss price from the message
    triggerPrice = stopLossPrice;
    // The stop loss order price should be 0.25% below the trigger price
    stopLossOrderPrice = triggerPrice * 0.9975;
  }

  // Round all prices to two decimal places ending in zero
  const roundedStopLossPrice =
    roundToTwoDecimalsEndingInZero(stopLossOrderPrice);
  const roundedTriggerPrice = roundToTwoDecimalsEndingInZero(triggerPrice);

  // Ensure we have valid numeric values
  const finalStopLossPrice = Number(roundedStopLossPrice);
  const finalTriggerPrice = Number(roundedTriggerPrice);

  // Validate the values
  if (isNaN(finalStopLossPrice) || isNaN(finalTriggerPrice)) {
    throw new Error("Invalid price values for stop loss order");
  }

  const orderData = {
    exchange: "NSE",
    symboltoken: Number(document.token),
    buyorsell: reversedTransactionType,
    ordertype: "STOPLOSS",
    producttype: "VALUEPLUS",
    orderduration: "DAY",
    price: finalStopLossPrice,
    triggerprice: finalTriggerPrice,
    quantityinlot: quantity,
    amoorder: "N",
  };

  return orderData;
}

// Round function for stop loss prices
function roundToTwoDecimalsEndingInZero(value) {
  let tickSize;

  if (value <= 250) {
    tickSize = 0.01;
  } else if (value <= 1000) {
    tickSize = 0.05;
  } else if (value <= 5000) {
    tickSize = 0.1;
  } else if (value <= 10000) {
    tickSize = 0.5;
  } else if (value <= 20000) {
    tickSize = 1.0;
  } else {
    tickSize = 5.0;
  }

  // Round the value to the nearest tick size
  const rounded = (Math.round(value / tickSize) * tickSize).toFixed(2);
  return rounded;
}

// Function to place order via API
async function placeOrder(orderData, credentials, authToken, apiKey, userId) {
  const config = {
    method: "post",
    url: "https://openapi.motilaloswal.com/rest/trans/v1/placeorder",
    headers: {
      Accept: "application/json",
      "User-Agent": "MOSL/V.1.1.0",
      Authorization: authToken,
      ApiKey: apiKey,
      ClientLocalIp: credentials.localIp,
      ClientPublicIp: credentials.publicIp,
      MacAddress: credentials.macAddress,
      SourceId: "WEB",
      vendorinfo: userId,
      osname: "Windows-10",
      osversion: "10.0.19041",
      devicemodel: "AHV",
      manufacturer: "DELL",
      productname: "Dellserver",
      productversion: "m3-48vcpu-384gb-intel",
      installedappid: "AppID",
      browsername: "Chrome",
      browserversion: "105.0",
    },
    data: JSON.stringify(orderData),
  };

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    // API request failed - silently handled
    throw error;
  }
}

module.exports = {
  placeOrder,
  createStopLossOrderData,
  createOrderData,
  handleClientOrder,
};
