import { Request, Response, NextFunction } from "express";
import rateLimit from "express-rate-limit";
import helmet from "helmet";
import cors from "cors";
import { appConfig } from "../config";
import { rateLimitError } from "./error";

// Rate limiter middleware
export const rateLimiter = rateLimit({
  windowMs: appConfig.security.rateLimitWindowMs,
  max: appConfig.security.rateLimitMaxRequests,
  message: "Too many requests from this IP, please try again later",
  handler: (req: Request, res: Response) => {
    throw rateLimitError("Rate limit exceeded");
  },
});

// CORS configuration
export const corsOptions = cors({
  origin: appConfig.security.allowedOrigins,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true,
  maxAge: 86400, // 24 hours
});

// Security headers middleware
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "https:"],
      fontSrc: ["'self'", "https:", "data:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: true,
  crossOriginResourcePolicy: { policy: "same-site" },
  dnsPrefetchControl: { allow: false },
  frameguard: { action: "deny" },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: { permittedPolicies: "none" },
  referrerPolicy: { policy: "strict-origin-when-cross-origin" },
  xssFilter: true,
});

// Request validation middleware
export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      next();
    } catch (error) {
      next(error);
    }
  };
};

// File upload validation middleware
export const validateFileUpload = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  if (!req.file) {
    return next();
  }

  const file = req.file;
  const maxSize = appConfig.upload.maxFileSize;
  const allowedExtensions = appConfig.upload.allowedExtensions;

  // Check file size
  if (file.size > maxSize) {
    return res.status(400).json({
      success: false,
      error: "File too large",
      message: `File size must be less than ${maxSize / 1024 / 1024}MB`,
    });
  }

  // Check file extension
  const ext = file.originalname.split(".").pop()?.toLowerCase();
  if (!ext || !allowedExtensions.includes(`.${ext}`)) {
    return res.status(400).json({
      success: false,
      error: "Invalid file type",
      message: `Only ${allowedExtensions.join(", ")} files are allowed`,
    });
  }

  next();
};

// API key validation middleware
export const validateApiKey = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const apiKey = req.headers["x-api-key"];

  if (!apiKey || apiKey !== appConfig.api.accessToken) {
    res.status(401).json({
      success: false,
      error: "Invalid API key",
      message: "Please provide a valid API key",
    });
    return;
  }

  next();
};

// Request sanitization middleware
export const sanitizeRequest = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Sanitize query parameters
  if (req.query) {
    Object.keys(req.query).forEach((key) => {
      if (typeof req.query[key] === "string") {
        req.query[key] = sanitizeString(req.query[key] as string);
      }
    });
  }

  // Sanitize request body
  if (req.body) {
    Object.keys(req.body).forEach((key) => {
      if (typeof req.body[key] === "string") {
        req.body[key] = sanitizeString(req.body[key]);
      }
    });
  }

  next();
};

// String sanitization helper
function sanitizeString(str: string): string {
  return str
    .replace(/[<>]/g, "") // Remove < and >
    .replace(/javascript:/gi, "") // Remove javascript: protocol
    .replace(/on\w+=/gi, "") // Remove on* attributes
    .trim();
}

// Error handling for security middleware
export const securityErrorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (err.name === "RateLimitExceeded") {
    res.status(429).json({
      success: false,
      error: "Rate limit exceeded",
      message: "Too many requests, please try again later",
    });
    return;
  }

  if (err.name === "CorsError") {
    res.status(403).json({
      success: false,
      error: "CORS error",
      message: "Cross-origin request not allowed",
    });
    return;
  }

  next(err);
};
