"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ws_1 = __importDefault(require("ws"));
class WebSocketService {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.heartbeatInterval = null;
        this.messageQueue = [];
        this.isConnected = false;
        this.messageHandlers = new Map();
        // Private constructor for singleton pattern
    }
    static getInstance() {
        if (!WebSocketService.instance) {
            WebSocketService.instance = new WebSocketService();
        }
        return WebSocketService.instance;
    }
    // Connect to WebSocket server
    async connect(url) {
        if (this.isConnected)
            return;
        try {
            this.ws = new ws_1.default(url);
            this.ws.on("open", () => {
                console.log("✅ WebSocket connection established");
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                this.processMessageQueue();
            });
            this.ws.on("message", (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleMessage(message);
                }
                catch (error) {
                    console.error("❌ Error parsing WebSocket message:", error);
                }
            });
            this.ws.on("close", () => {
                console.log("🔌 WebSocket connection closed");
                this.handleDisconnect();
            });
            this.ws.on("error", (error) => {
                console.error("❌ WebSocket error:", error);
                this.handleError(error);
            });
            // Wait for connection to be established
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error("WebSocket connection timeout"));
                }, parseInt(process.env.WEBSOCKET_CONNECTION_TIMEOUT || "30000"));
                this.ws.once("open", () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
        }
        catch (error) {
            console.error("❌ Failed to establish WebSocket connection:", error);
            throw new Error("Failed to establish WebSocket connection");
        }
    }
    // Send message to WebSocket server
    send(message) {
        if (!this.isConnected) {
            this.messageQueue.push(message);
            return;
        }
        try {
            this.ws.send(JSON.stringify(message));
        }
        catch (error) {
            console.error("❌ Error sending WebSocket message:", error);
            this.messageQueue.push(message);
        }
    }
    // Subscribe to message type
    subscribe(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }
    // Unsubscribe from message type
    unsubscribe(type, handler) {
        const handlers = this.messageHandlers.get(type);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
            }
        }
    }
    // Handle incoming messages
    handleMessage(message) {
        const handlers = this.messageHandlers.get(message.type);
        if (handlers) {
            handlers.forEach((handler) => {
                try {
                    handler(message.data);
                }
                catch (error) {
                    console.error(`❌ Error in message handler for ${message.type}:`, error);
                }
            });
        }
    }
    // Handle disconnection
    handleDisconnect() {
        this.isConnected = false;
        this.stopHeartbeat();
        const maxReconnectAttempts = parseInt(process.env.WEBSOCKET_RECONNECT_ATTEMPTS || "5");
        if (this.reconnectAttempts < maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${maxReconnectAttempts})...`);
            setTimeout(() => this.connect(process.env.WEBSOCKET_URL || ""), parseInt(process.env.WEBSOCKET_RECONNECT_DELAY || "5000"));
        }
        else {
            console.error("❌ Max reconnection attempts reached");
            throw new Error("Max reconnection attempts reached");
        }
    }
    // Handle errors
    handleError(error) {
        console.error("❌ WebSocket error:", error);
        this.handleDisconnect();
    }
    // Start heartbeat
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: "ping", data: {} });
            }
        }, parseInt(process.env.WEBSOCKET_HEARTBEAT_INTERVAL || "30000"));
    }
    // Stop heartbeat
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    // Process message queue
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            if (message) {
                this.send(message);
            }
        }
    }
    // Close WebSocket connection
    close() {
        if (this.ws) {
            this.stopHeartbeat();
            this.ws.close();
            this.ws = null;
            this.isConnected = false;
            this.messageQueue = [];
            this.messageHandlers.clear();
        }
    }
    // Check if connected
    isConnectedToServer() {
        return this.isConnected;
    }
}
exports.default = WebSocketService;
//# sourceMappingURL=websocket.js.map