import { Request, Response, NextFunction } from "express";
export declare class APIError extends Error {
    statusCode: number;
    code?: string | undefined;
    details?: any | undefined;
    constructor(statusCode: number, message: string, code?: string | undefined, details?: any | undefined);
}
export declare const errorHandler: (err: Error, req: Request, res: Response, next: NextFunction) => Response<any, Record<string, any>>;
export declare const notFoundHandler: (req: Request, res: Response) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validationError: (message: string, details?: any) => APIError;
export declare const authError: (message?: string) => APIError;
export declare const forbiddenError: (message?: string) => APIError;
export declare const notFoundError: (message?: string) => APIError;
export declare const rateLimitError: (message?: string) => APIError;
export declare const databaseError: (message?: string) => APIError;
export declare const websocketError: (message?: string) => APIError;
//# sourceMappingURL=error.d.ts.map