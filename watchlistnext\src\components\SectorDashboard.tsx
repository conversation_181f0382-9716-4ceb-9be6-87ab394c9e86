

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Building2,
  Activity,
} from "lucide-react";
import { useRouter } from "next/navigation";
import SectorAnalytics from "./SectorAnalytics";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
}

interface SectorData {
  sector: string;
  stocks: StockData[];
  totalStocks: number;
  gainers: number;
  losers: number;
  avgChange: number;
  totalVolume: number;
}

export default function SectorDashboard() {
  const router = useRouter();
  const [selectedExchange, setSelectedExchange] = useState<"NSE" | "BSE">("NSE");
  const [sectorData, setSectorData] = useState<SectorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"overview" | "analytics">("overview");
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // Debounced fetch function to prevent excessive API calls
  const fetchSectorData = useCallback(async (exchange: string) => {
    const now = Date.now();
    // Prevent fetching if less than 30 seconds have passed
    if (now - lastFetchTime < 30000) {
      return;
    }

    try {
      setLoading(true);
      setLastFetchTime(now);
      
      const response = await fetch(`/api/sectors?exchange=${exchange}&limit=1000`);
      
      if (!response.ok) {
        throw new Error(`API responded with status ${response.status}`);
      }
      
      const data = await response.json();

      if (data.success && data.sectors) {
        setSectorData(data.sectors);
      } else {
        setSectorData([]);
      }
    } catch (error) {
      console.error("Error fetching sector data:", error);
      setSectorData([]);
    } finally {
      setLoading(false);
    }
  }, [lastFetchTime]);

  // Fetch data when exchange changes
  useEffect(() => {
    fetchSectorData(selectedExchange);
  }, [selectedExchange, fetchSectorData]);

  const formatPrice = (price: number) => {
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const handleViewAllStocks = (sectorName: string) => {
    const encodedSector = encodeURIComponent(sectorName);
    router.push(`/sectors/${encodedSector}?exchange=${selectedExchange}`);
  };

  const handleExchangeChange = (exchange: "NSE" | "BSE") => {
    if (exchange !== selectedExchange) {
      setSelectedExchange(exchange);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading sector data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Professional Header */}
      <div className="page-header rounded-2xl p-8 shadow-large">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center justify-center w-16 h-16 bg-gradient-trading rounded-2xl shadow-glow">
              <Building2 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="header-title text-4xl font-bold mb-2">
                Sector Dashboard
              </h1>
              <p className="text-gray-600 text-lg">
                Professional sector analysis and market intelligence
              </p>
              <div className="flex items-center gap-4 mt-3">
                <div className="status-indicator live">
                  <span>Live Market Data</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Activity className="w-4 h-4" />
                  <span>Real-time Updates</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Exchange Toggle */}
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm rounded-xl p-2 border border-gray-200 shadow-soft">
              <Button
                variant={selectedExchange === "NSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => handleExchangeChange("NSE")}
                className="min-w-[80px] font-semibold"
              >
                NSE
              </Button>
              <Button
                variant={selectedExchange === "BSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => handleExchangeChange("BSE")}
                className="min-w-[80px] font-semibold"
              >
                BSE
              </Button>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-500">Exchange</p>
              <p className="text-sm font-semibold text-gray-700">
                {selectedExchange} Market
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tab Navigation */}
      <div className="bg-white rounded-2xl shadow-medium border border-gray-200 overflow-hidden">
        <nav className="flex">
          <button
            type="button"
            onClick={() => setActiveTab("overview")}
            className={`flex-1 py-4 px-6 font-semibold text-sm transition-all duration-200 ${
              activeTab === "overview"
                ? "bg-gradient-trading text-white shadow-lg"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <BarChart3 className="w-4 h-4" />
              <span>Market Overview</span>
            </div>
          </button>
          <button
            type="button"
            onClick={() => setActiveTab("analytics")}
            className={`flex-1 py-4 px-6 font-semibold text-sm transition-all duration-200 ${
              activeTab === "analytics"
                ? "bg-gradient-trading text-white shadow-lg"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Activity className="w-4 h-4" />
              <span>Advanced Analytics</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && (
        <>
          {/* Market Status Notice */}
          {sectorData.length === 0 && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 rounded-full bg-orange-500 animate-pulse"></div>
                  <div>
                    <p className="font-medium text-orange-800">
                      {selectedExchange} Market Data Loading
                    </p>
                    <p className="text-sm text-orange-600">
                      Real-time data will be available during market hours (9:15 AM - 3:30 PM IST).
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Enhanced Sector Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Total Sectors
                    </p>
                    <p className="text-3xl font-bold text-gray-900 price-ticker">
                      {sectorData.length}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">Active markets</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Total Stocks
                    </p>
                    <div className="flex items-baseline gap-2">
                      <p className="text-3xl font-bold text-gray-900 price-ticker">
                        {sectorData.reduce((sum, sector) => sum + sector.totalStocks, 0)}
                      </p>
                      <p className="text-sm text-green-600 font-semibold">
                        / {selectedExchange === "NSE" ? "2103" : "4323"}
                      </p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Active Stocks ({selectedExchange})
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                    <Building2 className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Gainers
                    </p>
                    <p className="text-3xl font-bold text-green-600 price-ticker">
                      {sectorData.reduce((sum, sector) => sum + sector.gainers, 0)}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Bullish stocks
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Losers
                    </p>
                    <p className="text-3xl font-bold text-red-600 price-ticker">
                      {sectorData.reduce((sum, sector) => sum + sector.losers, 0)}
                    </p>
                    <p className="text-xs text-red-600 mt-1">Bearish stocks</p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated" className="trading-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Market Coverage
                    </p>
                    <div className="flex items-baseline gap-2">
                      <p className="text-2xl font-bold text-blue-600 price-ticker">
                        {((sectorData.reduce((sum, sector) => sum + sector.totalStocks, 0) /
                          (selectedExchange === "NSE" ? 2103 : 4323)) * 100).toFixed(1)}%
                      </p>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Live coverage
                    </p>
                  </div>
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                    <Activity className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Sector Cards */}
          <div className="sector-grid">
            {sectorData.map((sector) => (
              <Card
                key={sector.sector}
                variant="elevated"
                className="trading-card group cursor-pointer"
                onClick={() => handleViewAllStocks(sector.sector)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {sector.sector}
                    </CardTitle>
                    <Badge
                      variant={sector.avgChange >= 0 ? "success" : "destructive"}
                      size="lg"
                      className="font-semibold"
                    >
                      {sector.avgChange >= 0 ? "+" : ""}
                      {sector.avgChange.toFixed(2)}%
                    </Badge>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-gray-900">
                        {sector.totalStocks}
                      </p>
                      <p className="text-xs text-gray-600">Total Stocks</p>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-green-600">
                        {sector.gainers}
                      </p>
                      <p className="text-xs text-green-600">Gainers</p>
                    </div>
                    <div className="bg-red-50 rounded-lg p-3">
                      <p className="text-lg font-bold text-red-600">
                        {sector.losers}
                      </p>
                      <p className="text-xs text-red-600">Losers</p>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-700">
                      Top Performers
                    </h4>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Activity className="w-3 h-3" />
                      <span>Live</span>
                    </div>
                  </div>

                  {/* Top 3 stocks in sector */}
                  {sector.stocks.slice(0, 3).map((stock, index) => (
                    <div
                      key={stock.securityId}
                      className="stock-row group/stock"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 flex-1">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg text-xs font-bold text-blue-600">
                            {index + 1}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-sm text-gray-900 group-hover/stock:text-blue-600 transition-colors">
                              {stock.ticker}
                            </div>
                            <div className="text-xs text-gray-500 truncate">
                              {stock.companyName}
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="font-bold text-sm price-ticker">
                            {formatPrice(stock.ltp)}
                          </div>
                          <div
                            className={`text-xs font-semibold ${
                              stock.change >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {formatChange(stock.change, stock.changePercent)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {sector.stocks.length > 3 && (
                    <div className="pt-4 border-t border-gray-100">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full font-semibold group-hover:bg-blue-50 group-hover:border-blue-300 group-hover:text-blue-600 transition-all"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewAllStocks(sector.sector);
                        }}
                      >
                        View all {sector.stocks.length} stocks
                        <svg
                          className="w-4 h-4 ml-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}

      {/* Analytics Tab */}
      {activeTab === "analytics" && (
        <SectorAnalytics
          sectorData={sectorData}
          selectedExchange={selectedExchange}
        />
      )}
    </div>
  );
}
