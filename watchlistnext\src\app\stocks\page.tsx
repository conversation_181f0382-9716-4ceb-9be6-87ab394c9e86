"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import { createWebSocketConnection, cleanupWebSocketConnection, removeWebSocketListeners } from "@/utils/websocket";
import Link from "next/link";
import StocksColumnLayout from "../../components/StocksColumnLayout";
import ConnectionStatus from "../../components/ConnectionStatus";
import Stats from "../../components/Stats";
import Navigation from "../../components/Navigation";
import KeyIndices from "../../components/KeyIndices";
import ErrorBoundary from "../../components/ErrorBoundary";
import { logger } from "../../services/logger";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface Stats {
  instrumentCount: number;
  updateRate: number;
  latency: number;
  lastUpdate: string;
}

export default function StocksPage() {
  const [isConnected, setIsConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(
    new Map()
  );
  const [stats, setStats] = useState<Stats>({
    instrumentCount: 0,
    updateRate: 0,
    latency: 0,
    lastUpdate: "Never",
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Use refs to prevent stale closures
  const socketRef = useRef<any>(null);
  const mountedRef = useRef(true);

  // Memoized callbacks to prevent unnecessary re-renders
  const handleConnect = useCallback(() => {
    if (!mountedRef.current) return;
    logger.info("Stocks page connected to WebSocket");
    setIsConnected(true);
    setError(null);
    setIsLoading(false);
  }, []);

  const handleDisconnect = useCallback((reason: string) => {
    if (!mountedRef.current) return;
    logger.warn("Stocks page disconnected from WebSocket:", reason);
    setIsConnected(false);
    setIsLoading(false);
  }, []);

  const handleError = useCallback((error: Error) => {
    if (!mountedRef.current) return;
    logger.error("WebSocket error in stocks page:", error);
    setError(error.message);
    setIsLoading(false);
  }, []);

  const handleMarketData = useCallback((data: MarketData) => {
    if (!mountedRef.current) return;
    setMarketData((prev) => {
      const newMarketData = new Map(prev);
      newMarketData.set(data.securityId, data);
      return newMarketData;
    });
  }, []);

  const handleMarketDataBatch = useCallback((batch: MarketData[]) => {
    if (!mountedRef.current) return;
    setMarketData((prev) => {
      const newMarketData = new Map(prev);
      batch.forEach((item) => {
        newMarketData.set(item.securityId, item);
      });
      return newMarketData;
    });
  }, []);

  const handleInitialData = useCallback((data: {
    instruments: Record<string, unknown>[];
    liveData: MarketData[];
  }) => {
    if (!mountedRef.current) return;
    setStats((prev) => ({
      ...prev,
      instrumentCount: data.instruments.length,
    }));

    setMarketData((prev) => {
      const newMarketData = new Map(prev);
      data.liveData.forEach((item: MarketData) => {
        newMarketData.set(item.securityId, item);
      });
      return newMarketData;
    });
  }, []);

  const handleStatus = useCallback((data: Record<string, unknown>) => {
    if (!mountedRef.current) return;
    setStats((prev) => ({
      ...prev,
      updateRate: (data.updateRate as number) || 0,
      latency: (data.latency as number) || 0,
      lastUpdate: new Date().toLocaleTimeString(),
    }));
  }, []);

  // Fetch initial stats
  const fetchInitialStats = useCallback(async () => {
    try {
      const response = await fetch("http://localhost:8080/api/data");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      if (mountedRef.current) {
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.totalInstruments || data.instruments || 0,
        }));
      }
    } catch (error) {
      logger.warn("Could not fetch initial stats from WebSocket server:", error);
      // Don't show error to user, just log it - WebSocket will provide the data
    }
  }, []);

  useEffect(() => {
    let isMounted = true;
    mountedRef.current = true;
    
    const setupWebSocket = async () => {
      try {
        // Use shared WebSocket connection to prevent multiple connections
        const socketInstance = await createWebSocketConnection({
          onConnect: handleConnect,
          onDisconnect: handleDisconnect,
          onError: handleError,
          onMarketData: handleMarketData,
          onMarketDataBatch: handleMarketDataBatch,
        });

        if (!isMounted) return;

        socketRef.current = socketInstance;

        // Add event listeners
        socketInstance.on("initialData", handleInitialData);
        socketInstance.on("status", handleStatus);

        // Try to fetch initial stats, but don't block if it fails
        fetchInitialStats();
      } catch (error) {
        if (!isMounted) return;
        logger.error("Error setting up WebSocket connection:", error);
        setError("Failed to connect to market data");
        setIsLoading(false);
      }
    };

    setupWebSocket();

    return () => {
      isMounted = false;
      mountedRef.current = false;
      
      // Clean up event listeners and remove from shared connection
      if (socketRef.current) {
        removeWebSocketListeners(handleMarketData, handleMarketDataBatch);
        socketRef.current.off("initialData", handleInitialData);
        socketRef.current.off("status", handleStatus);
        socketRef.current = null;
      }

      // Only cleanup the WebSocket if there are no more clients
      cleanupWebSocketConnection();
    };
  }, [handleConnect, handleDisconnect, handleError, handleMarketData, handleMarketDataBatch, handleInitialData, handleStatus, fetchInitialStats]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
        <Navigation />
        <main className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Connecting to market data...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
        <Navigation />
        <main className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-center">
                <svg className="w-6 h-6 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h3 className="text-lg font-semibold text-red-800">Connection Error</h3>
                  <p className="text-red-600">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Retry Connection
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20">
        <Navigation />

        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none" />

        <main className="relative z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Enhanced Header */}
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 space-y-4 lg:space-y-0">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-12 h-12 bg-gradient-trading rounded-2xl shadow-lg">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold gradient-text">
                      Stocks Market Overview
                    </h1>
                    <p className="text-gray-600 text-sm">
                      Real-time market data and analytics
                    </p>
                  </div>
                </div>
                <Link
                  href="/market-overview"
                  className="hidden sm:inline-flex items-center px-4 py-2 bg-gradient-trading text-white rounded-lg text-sm font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200 active:scale-95"
                >
                  <span>Detailed View</span>
                  <svg
                    className="w-4 h-4 ml-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Link>
              </div>
              <ConnectionStatus isConnected={isConnected} />
            </div>

            {/* Enhanced Stats */}
            <div className="mb-8">
              <Stats stats={stats} />
            </div>

            {/* Enhanced Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8">
              {/* Key Indices Sidebar */}
              <div className="lg:col-span-1 space-y-6">
                <div className="sticky top-24">
                  <KeyIndices />
                </div>
              </div>

              {/* Main Stocks Column Layout */}
              <div className="lg:col-span-3">
                <div className="fade-in-up">
                  <StocksColumnLayout data={Array.from(marketData.values())} />
                </div>
              </div>
            </div>

            {/* Enhanced Market Insights */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-large border border-white/20 p-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-trading rounded-xl">
                  <span className="text-white text-lg">📈</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">
                    Market Insights
                  </h3>
                  <p className="text-sm text-gray-600">
                    Live market sentiment analysis
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center p-6 bg-green-50 rounded-xl border border-green-200">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {
                      Array.from(marketData.values()).filter(
                        (stock) => stock.change > 0
                      ).length
                    }
                  </div>
                  <div className="text-sm font-medium text-green-700">
                    Stocks Up
                  </div>
                  <div className="text-xs text-green-600 mt-1">
                    Bullish sentiment
                  </div>
                </div>

                <div className="text-center p-6 bg-red-50 rounded-xl border border-red-200">
                  <div className="text-3xl font-bold text-red-600 mb-2">
                    {
                      Array.from(marketData.values()).filter(
                        (stock) => stock.change < 0
                      ).length
                    }
                  </div>
                  <div className="text-sm font-medium text-red-700">
                    Stocks Down
                  </div>
                  <div className="text-xs text-red-600 mt-1">
                    Bearish sentiment
                  </div>
                </div>

                <div className="text-center p-6 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="text-3xl font-bold text-gray-600 mb-2">
                    {
                      Array.from(marketData.values()).filter(
                        (stock) => stock.change === 0
                      ).length
                    }
                  </div>
                  <div className="text-sm font-medium text-gray-700">
                    Unchanged
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    Neutral sentiment
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ErrorBoundary>
  );
}
