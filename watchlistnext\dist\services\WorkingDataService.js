"use strict";
// ============================================================================
// WORKING DATA SERVICE - Direct connection with tested credentials
// ============================================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.workingDataService = exports.WorkingDataService = void 0;
const pg_1 = require("pg");
const LoggerService_1 = require("./LoggerService");
class WorkingDataService {
    constructor() {
        this.isConnected = false;
        // Use the working connection string without SSL issues
        this.pool = new pg_1.Pool({
            connectionString: "postgresql://postgres.fjognbnryybyeepcoukr:<EMAIL>:5432/postgres",
            ssl: false, // Disable SSL to avoid SASL issues
            max: 10, // Reduced pool size
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 20000, // Increased timeout
            maxUses: 7500,
        });
        this.pool.on("connect", () => {
            this.isConnected = true;
            LoggerService_1.logger.info("Working database client connected");
        });
        this.pool.on("error", (err) => {
            this.isConnected = false;
            LoggerService_1.logger.error("Working database pool error", { error: err.message });
        });
    }
    /**
     * Load all instruments from database for WebSocket subscription
     */
    async loadAllInstruments(maxInstruments = 25000) {
        try {
            const client = await this.pool.connect();
            try {
                // Get NSE instruments
                const nseQuery = `
          SELECT 
            nse_security_id as security_id,
            symbol as ticker,
            'NSE_EQ' as exchange,
            1 as exchange_code
          FROM company_list 
          WHERE nse_security_id IS NOT NULL 
          AND nse_security_id != '' 
          AND nse_security_id != '-'
          AND symbol IS NOT NULL
          ORDER BY company_name
          LIMIT $1
        `;
                // Get BSE instruments
                const bseQuery = `
          SELECT
            bse_security_id as security_id,
            symbol as ticker,
            'BSE_EQ' as exchange,
            4 as exchange_code
          FROM company_list
          WHERE bse_security_id IS NOT NULL
          AND bse_security_id != ''
          AND bse_security_id != '-'
          AND symbol IS NOT NULL
          ORDER BY company_name
          LIMIT $1
        `;
                const [nseResult, bseResult] = await Promise.all([
                    client.query(nseQuery, [Math.floor(maxInstruments / 2)]),
                    client.query(bseQuery, [Math.floor(maxInstruments / 2)]),
                ]);
                const instruments = [];
                // Add NSE instruments
                nseResult.rows.forEach((row) => {
                    if (row.security_id && row.ticker) {
                        instruments.push({
                            securityId: parseInt(row.security_id),
                            ticker: row.ticker,
                            exchange: "NSE_EQ",
                            exchangeCode: 1,
                            segment: "NSE_EQ",
                            lotUnits: 1,
                        });
                    }
                });
                // Add BSE instruments
                bseResult.rows.forEach((row) => {
                    if (row.security_id && row.ticker) {
                        instruments.push({
                            securityId: parseInt(row.security_id),
                            ticker: row.ticker,
                            exchange: "BSE_EQ",
                            exchangeCode: 4, // Fixed: Use correct BSE_EQ exchange code (4, not 6)
                            segment: "BSE_EQ",
                            lotUnits: 1,
                        });
                    }
                });
                LoggerService_1.logger.info("Successfully loaded instruments from database", {
                    total: instruments.length,
                    nse: nseResult.rows.length,
                    bse: bseResult.rows.length,
                });
                return instruments;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            LoggerService_1.logger.error("Error loading instruments from database", {
                error: error.message,
            });
            // Return empty array to trigger fallback
            return [];
        }
    }
    /**
     * Get companies by exchange for instrument loading
     */
    async getCompaniesByExchange(exchange, limit = 12500, offset = 0) {
        try {
            const client = await this.pool.connect();
            try {
                let query = "";
                if (exchange === "NSE") {
                    query = `
            SELECT 
              company_name,
              symbol,
              nse_security_id,
              sector_name,
              industry_new_name
            FROM company_list 
            WHERE nse_security_id IS NOT NULL 
            AND nse_security_id != '' 
            AND nse_security_id != '-'
            AND symbol IS NOT NULL
            ORDER BY company_name
            LIMIT $1 OFFSET $2
          `;
                }
                else if (exchange === "BSE") {
                    query = `
            SELECT 
              company_name,
              symbol,
              bse_security_id,
              sector_name,
              industry_new_name
            FROM company_list 
            WHERE bse_security_id IS NOT NULL 
            AND bse_security_id != '' 
            AND bse_security_id != '-'
            AND symbol IS NOT NULL
            ORDER BY company_name
            LIMIT $1 OFFSET $2
          `;
                }
                const result = await client.query(query, [limit, offset]);
                LoggerService_1.logger.info(`Loaded ${result.rows.length} ${exchange} companies from database`);
                return result.rows;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            LoggerService_1.logger.error(`Error loading ${exchange} companies`, {
                error: error.message,
            });
            return [];
        }
    }
    /**
     * Search for a company by symbol
     */
    async findCompanyBySymbol(symbol) {
        try {
            const client = await this.pool.connect();
            try {
                const query = `
          SELECT
            company_name,
            symbol,
            nse_security_id,
            bse_security_id,
            sector_name,
            industry_new_name
          FROM company_list
          WHERE symbol = $1
          LIMIT 1
        `;
                const result = await client.query(query, [symbol.toUpperCase()]);
                if (result.rows.length > 0) {
                    LoggerService_1.logger.info(`Found company by symbol`, {
                        symbol,
                        company: result.rows[0].company_name,
                        nseId: result.rows[0].nse_security_id,
                        bseId: result.rows[0].bse_security_id,
                    });
                    return result.rows[0];
                }
                LoggerService_1.logger.warn(`Company not found by symbol`, { symbol });
                return null;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            LoggerService_1.logger.error(`Error searching for company by symbol`, {
                symbol,
                error: error.message,
            });
            return null;
        }
    }
    /**
     * Check database connection
     */
    async checkConnection() {
        try {
            const client = await this.pool.connect();
            try {
                await client.query("SELECT 1");
                this.isConnected = true;
                return true;
            }
            finally {
                client.release();
            }
        }
        catch (error) {
            this.isConnected = false;
            LoggerService_1.logger.error("Working database connection check failed", {
                error: error.message,
            });
            return false;
        }
    }
    /**
     * Get connection status
     */
    isConnectionHealthy() {
        return this.isConnected;
    }
    /**
     * Close all connections
     */
    async close() {
        await this.pool.end();
        this.isConnected = false;
        LoggerService_1.logger.info("Working database pool closed");
    }
}
exports.WorkingDataService = WorkingDataService;
// Singleton instance
exports.workingDataService = new WorkingDataService();
//# sourceMappingURL=WorkingDataService.js.map