import { DatabaseService } from "../../../services/database";

const db = DatabaseService.getInstance();

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Get query parameters
    const sectorParam = searchParams.get("sector");
    const industryParam = searchParams.get("industry");
    const nse_symbol = searchParams.get("symbol");
    const isin_no = searchParams.get("isin");
    const search = searchParams.get("search");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const action = searchParams.get("action");

    // Handle different actions
    switch (action) {
      case "stats":
        const stats = await db.getStats();
        return new Response(
          JSON.stringify({
            success: true,
            data: stats,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "sectors":
        const sectors = await db.getSectors();
        return new Response(
          JSON.stringify({
            success: true,
            data: sectors,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "industries":
        const industries = await db.getIndustries(sectorParam || undefined);
        return new Response(
          JSON.stringify({
            success: true,
            data: industries,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "sub-sectors":
        const industryFilter = searchParams.get("industry");
        const subSectors = await db.getSubSectors(industryFilter || undefined);
        return new Response(
          JSON.stringify({
            success: true,
            data: subSectors,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "micro-categories":
        const subSectorFilter = searchParams.get("sub_sector");
        const microCategories = await db.getMicroCategories(
          subSectorFilter || undefined
        );
        return new Response(
          JSON.stringify({
            success: true,
            data: microCategories,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "sector-distribution":
        const distribution = await db.getSectorDistribution();
        return new Response(
          JSON.stringify({
            success: true,
            data: distribution,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "by-sector":
        if (!sectorParam) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Sector parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        const sectorCompanies = await db.getCompaniesBySector(sectorParam);
        return new Response(
          JSON.stringify({
            success: true,
            data: sectorCompanies,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "by-industry":
        const industryQueryParam = searchParams.get("industry");
        if (!industryQueryParam) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Industry parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        const industryCompanies =
          await db.getCompaniesByIndustry(industryQueryParam);
        return new Response(
          JSON.stringify({
            success: true,
            data: industryCompanies,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "by-sub-sector":
        const subSectorQueryParam = searchParams.get("sub_sector");
        if (!subSectorQueryParam) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Sub-sector parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        const subSectorCompanies =
          await db.getCompaniesBySubSector(subSectorQueryParam);
        return new Response(
          JSON.stringify({
            success: true,
            data: subSectorCompanies,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "by-micro-category":
        const microCategoryQueryParam = searchParams.get("micro_category");
        if (!microCategoryQueryParam) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Micro-category parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        const microCategoryCompanies = await db.getCompaniesByMicroCategory(
          microCategoryQueryParam
        );
        return new Response(
          JSON.stringify({
            success: true,
            data: microCategoryCompanies,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      default:
        // Search companies with filters
        const offset = (page - 1) * limit;
        const result = await db.searchCompanies({
          sector: sectorParam || undefined,
          industry: industryParam || undefined,
          nse_symbol: nse_symbol || undefined,
          isin_no: isin_no || undefined,
          search: search || undefined,
          limit,
          offset,
        });

        return new Response(
          JSON.stringify({
            success: true,
            data: result.companies,
            pagination: {
              page: result.page,
              limit,
              total: result.total,
              totalPages: result.totalPages,
            },
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );
    }
  } catch (error) {
    console.error("Companies API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { symbols } = body;

    if (!symbols || !Array.isArray(symbols)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Symbols array is required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get multiple companies by symbols
    const companies = await Promise.all(
      symbols.map((symbol) => db.getCompanyBySymbol(symbol))
    );

    const validCompanies = companies.filter((company) => company !== null);

    return new Response(
      JSON.stringify({
        success: true,
        data: validCompanies,
        found: validCompanies.length,
        requested: symbols.length,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Companies POST API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
