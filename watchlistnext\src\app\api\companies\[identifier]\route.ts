import PrismaService from "../../../../services/PrismaService";

const prismaService = PrismaService.getInstance();

export async function GET(
  request: Request,
  { params }: { params: Promise<{ identifier: string }> }
) {
  try {
    const resolvedParams = await params;
    const identifier = resolvedParams.identifier;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "symbol"; // symbol, isin, security_id

    let company = null;

    switch (type) {
      case "isin":
        // Find by ISIN using Prisma
        const isinResult = await prismaService
          .getClient()
          .companyList.findFirst({
            where: {
              isin_no: {
                equals: identifier,
                mode: "insensitive",
              },
            },
          });
        if (isinResult) {
          company = {
            id: isinResult.id,
            company_name: isinResult.company_name,
            nse_security_id: isinResult.nse_security_id,
            bse_security_id: isinResult.bse_security_id,
            sector_name: isinResult.sector_name,
            industry_name: isinResult.industry_new_name,
            isin_no: isinResult.isin_no,
            symbol: isinResult.symbol,
          };
        }
        break;
      case "security_id":
        // Find by security ID using Prisma
        const securityResult = await prismaService
          .getClient()
          .companyList.findFirst({
            where: {
              OR: [
                { nse_security_id: identifier },
                { bse_security_id: identifier },
              ],
            },
          });
        if (securityResult) {
          company = {
            id: securityResult.id,
            company_name: securityResult.company_name,
            nse_security_id: securityResult.nse_security_id,
            bse_security_id: securityResult.bse_security_id,
            sector_name: securityResult.sector_name,
            industry_name: securityResult.industry_new_name,
            isin_no: securityResult.isin_no,
            symbol: securityResult.symbol,
          };
        }
        break;
      case "symbol":
      default:
        const symbolResult =
          await prismaService.findCompanyBySymbol(identifier);
        if (symbolResult.found) {
          company = {
            id: 0, // Prisma service doesn't return ID
            company_name: symbolResult.companyName,
            nse_security_id: symbolResult.nseId,
            bse_security_id: symbolResult.bseId,
            sector_name: symbolResult.sector,
            industry_name: symbolResult.industry,
            isin_no: symbolResult.isin,
            symbol: symbolResult.symbol,
          };
        }
        break;
    }

    if (!company) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Company not found",
          identifier,
          type,
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: company,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Company lookup API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
