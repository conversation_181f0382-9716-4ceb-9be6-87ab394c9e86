import { PrismaClient } from "@prisma/client";
declare class PrismaService {
    private static instance;
    private prisma;
    private constructor();
    static getInstance(): PrismaService;
    getClient(): PrismaClient;
    findCompanyBySymbol(symbol: string): Promise<{
        found: boolean;
        ticker: string;
        companyName: string | null;
        symbol: string | null;
        nseId: string | null;
        bseId: string | null;
        sector: string | null;
        industry: string | null;
        subSector: string | null;
        microCategory: string | null;
        isin: string | null;
        error?: never;
    } | {
        found: boolean;
        ticker: string;
        companyName?: never;
        symbol?: never;
        nseId?: never;
        bseId?: never;
        sector?: never;
        industry?: never;
        subSector?: never;
        microCategory?: never;
        isin?: never;
        error?: never;
    } | {
        found: boolean;
        ticker: string;
        error: string;
        companyName?: never;
        symbol?: never;
        nseId?: never;
        bseId?: never;
        sector?: never;
        industry?: never;
        subSector?: never;
        microCategory?: never;
        isin?: never;
    }>;
    getAllCompanies(limit?: number): Promise<{
        symbol: string | null;
        companyName: string | null;
        nseId: string | null;
        bseId: string | null;
        sector: string | null;
        industry: string | null;
        subSector: string | null;
        microCategory: string | null;
        isin: string | null;
    }[]>;
    getCompaniesBySector(sector: string): Promise<{
        symbol: string | null;
        companyName: string | null;
        nseId: string | null;
        bseId: string | null;
        sector: string | null;
        industry: string | null;
        subSector: string | null;
        microCategory: string | null;
    }[]>;
    updateMarketData(securityId: string, exchange: string, data: any): Promise<{
        symbol: string;
        exchange: string;
        id: number;
        security_id: string;
        ltp: number | null;
        change: number | null;
        change_percent: number | null;
        volume: bigint | null;
        high: number | null;
        low: number | null;
        open: number | null;
        close: number | null;
        timestamp: Date;
        last_updated: Date;
    } | null>;
    getMarketData(securityId: string, exchange: string): Promise<{
        symbol: string;
        exchange: string;
        id: number;
        security_id: string;
        ltp: number | null;
        change: number | null;
        change_percent: number | null;
        volume: bigint | null;
        high: number | null;
        low: number | null;
        open: number | null;
        close: number | null;
        timestamp: Date;
        last_updated: Date;
    } | null>;
    getLatestMarketDataForSymbol(symbol: string): Promise<{
        symbol: string;
        exchange: string;
        id: number;
        security_id: string;
        ltp: number | null;
        change: number | null;
        change_percent: number | null;
        volume: bigint | null;
        high: number | null;
        low: number | null;
        open: number | null;
        close: number | null;
        timestamp: Date;
        last_updated: Date;
    } | null>;
    createWatchlist(name: string, description?: string): Promise<{
        name: string;
        id: number;
        created_at: Date;
        updated_at: Date;
        description: string | null;
    } | null>;
    addToWatchlist(watchlistId: number, symbol: string, exchange: string): Promise<{
        symbol: string;
        exchange: string;
        id: number;
        watchlist_id: number;
        added_at: Date;
    } | null>;
    updateIndexData(symbol: string, data: any): Promise<{
        symbol: string;
        value: number | null;
        name: string;
        id: number;
        created_at: Date;
        change: number | null;
        change_percent: number | null;
        last_updated: Date;
    } | null>;
    getAllIndexData(): Promise<{
        symbol: string;
        value: number | null;
        name: string;
        id: number;
        created_at: Date;
        change: number | null;
        change_percent: number | null;
        last_updated: Date;
    }[]>;
    disconnect(): Promise<void>;
}
export default PrismaService;
//# sourceMappingURL=PrismaService.d.ts.map