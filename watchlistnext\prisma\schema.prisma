// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model CompanyList {
  id                Int      @id @default(autoincrement())
  company_name      String?
  isin_no          String?
  instrument       String?
  sector_name      String?
  industry_new_name String?
  sub_sector       String?
  micro_category   String?
  bse_security_id  String?
  nse_security_id  String?
  symbol           String?
  created_at       DateTime? @default(now())
  updated_at       DateTime? @updatedAt

  @@map("company_list")
}

model MarketData {
  id              Int      @id @default(autoincrement())
  security_id     String
  exchange        String
  symbol          String
  ltp             Float?   @default(0)
  change          Float?   @default(0)
  change_percent  Float?   @default(0)
  volume          BigInt?  @default(0)
  high            Float?   @default(0)
  low             Float?   @default(0)
  open            Float?   @default(0)
  close           Float?   @default(0)
  timestamp       DateTime @default(now())
  last_updated    DateTime @updatedAt

  @@unique([security_id, exchange], name: "security_id_exchange")
  @@map("market_data")
}

model Watchlist {
  id          Int      @id @default(autoincrement())
  name        String
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  items       WatchlistItem[]

  @@map("watchlists")
}

model WatchlistItem {
  id           Int      @id @default(autoincrement())
  watchlist_id Int
  symbol       String
  exchange     String
  added_at     DateTime @default(now())
  
  watchlist    Watchlist @relation(fields: [watchlist_id], references: [id], onDelete: Cascade)

  @@unique([watchlist_id, symbol, exchange])
  @@map("watchlist_items")
}

model IndexData {
  id             Int      @id @default(autoincrement())
  name           String
  symbol         String   @unique
  value          Float?   @default(0)
  change         Float?   @default(0)
  change_percent Float?   @default(0)
  last_updated   DateTime @updatedAt
  created_at     DateTime @default(now())

  @@map("index_data")
}
