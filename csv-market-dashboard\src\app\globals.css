@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the dashboard */
.glass {
  @apply bg-white/80 backdrop-blur-sm border border-white/20;
}

.glass-dark {
  @apply bg-gray-900/80 backdrop-blur-sm border border-gray-700/20;
}

.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

.gradient-text-success {
  @apply bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent;
}

.gradient-text-danger {
  @apply bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent;
}

/* Animation for market data updates */
@keyframes pulse-green {
  0%, 100% {
    @apply bg-green-100;
  }
  50% {
    @apply bg-green-200;
  }
}

@keyframes pulse-red {
  0%, 100% {
    @apply bg-red-100;
  }
  50% {
    @apply bg-red-200;
  }
}

.price-up {
  animation: pulse-green 0.5s ease-in-out;
}

.price-down {
  animation: pulse-red 0.5s ease-in-out;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-4 border-gray-300 border-t-blue-600;
}

/* Scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-lg;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-lg;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Table styling */
.market-table {
  @apply w-full border-collapse;
}

.market-table th {
  @apply bg-gray-50 px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider border-b border-gray-200;
}

.market-table td {
  @apply px-4 py-3 text-sm text-gray-900 border-b border-gray-100;
}

.market-table tr:hover {
  @apply bg-gray-50;
}

/* Status indicators */
.status-connected {
  @apply bg-green-500 text-white;
}

.status-disconnected {
  @apply bg-red-500 text-white;
}

.status-connecting {
  @apply bg-yellow-500 text-white;
}

/* Card hover effects */
.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:scale-105;
}

/* Filter panel */
.filter-panel {
  @apply bg-white rounded-lg shadow-md p-4 border border-gray-200;
}

.filter-group {
  @apply mb-4;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.filter-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.filter-checkbox {
  @apply mr-2 text-blue-600 focus:ring-blue-500;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .mobile-hidden {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
}
