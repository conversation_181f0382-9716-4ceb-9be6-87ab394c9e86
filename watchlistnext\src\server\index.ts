// ============================================================================
// MAIN SERVER ENTRY POINT - TypeScript Optimized
// ============================================================================

import "dotenv/config";
import { DhanMarketFeedServer } from "./MarketFeedServer";
import { logger } from "@/services/LoggerService";

// Global error handlers
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception", {
    error: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection", { reason, promise });
  process.exit(1);
});

// Graceful shutdown handler
let server: DhanMarketFeedServer | null = null;

const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);

  if (server) {
    try {
      await server.shutdown();
      logger.info("Graceful shutdown completed");
      process.exit(0);
    } catch (error) {
      logger.error("Error during graceful shutdown", {
        error: (error as Error).message,
      });
      process.exit(1);
    }
  } else {
    process.exit(0);
  }
};

// Register shutdown handlers
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Main function
async function main() {
  try {
    logger.info("Starting Dhan Market Feed Server...");

    // Create and start server
    server = new DhanMarketFeedServer();
    await server.start();

    logger.info("Server startup completed successfully");
  } catch (error) {
    logger.error("Failed to start server", { error: (error as Error).message });
    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  logger.error("Application startup failed", { error: error.message });
  process.exit(1);
});
