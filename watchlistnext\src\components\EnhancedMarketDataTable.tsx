import React, { useMemo, memo } from "react";
import <PERSON> from "next/link";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface EnhancedMarketDataTableProps {
  data: MarketData[];
  sortField: keyof MarketData;
  sortDirection: "asc" | "desc";
  onSort: (field: keyof MarketData) => void;
}

const EnhancedMarketDataTable: React.FC<EnhancedMarketDataTableProps> = ({
  data,
  sortField,
  sortDirection,
  onSort,
}) => {
  const formatNumber = useMemo(() => {
    return (num: number, decimals: number = 2): string => {
      return num.toLocaleString("en-IN", {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
      });
    };
  }, []);

  const formatVolume = useMemo(() => {
    return (volume: number): string => {
      if (volume >= 10000000) {
        return `${(volume / 10000000).toFixed(1)}Cr`;
      } else if (volume >= 100000) {
        return `${(volume / 100000).toFixed(1)}L`;
      } else if (volume >= 1000) {
        return `${(volume / 1000).toFixed(1)}K`;
      }
      return volume.toString();
    };
  }, []);

  const SortableHeader = memo<{
    field: keyof MarketData;
    children: React.ReactNode;
    className?: string;
  }>(({ field, children, className = "" }) => (
    <th
      className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ${className}`}
      onClick={() => onSort(field)}
    >
      <div className="flex items-center space-x-1">
        <span>{children}</span>
        <div className="flex flex-col">
          <svg
            className={`w-3 h-3 ${
              sortField === field && sortDirection === "asc"
                ? "text-blue-600"
                : "text-gray-400"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
              clipRule="evenodd"
            />
          </svg>
          <svg
            className={`w-3 h-3 -mt-1 ${
              sortField === field && sortDirection === "desc"
                ? "text-blue-600"
                : "text-gray-400"
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
    </th>
  ));

  SortableHeader.displayName = "SortableHeader";

  const TableRow = memo<{ item: MarketData }>(({ item }) => (
    <tr key={item.securityId} className="hover:bg-gray-50 transition-colors">
      <td className="px-4 py-3 whitespace-nowrap">
        <div className="flex flex-col">
          <Link
            href={`/stock/${item.ticker}`}
            className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors cursor-pointer"
          >
            {item.ticker}
          </Link>
          <div className="text-xs text-gray-500">{item.securityId}</div>
        </div>
      </td>
      <td className="px-4 py-3 whitespace-nowrap">
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {item.exchange}
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900">
        ₹{formatNumber(item.ltp)}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm">
        <span
          className={`font-medium ${
            item.change >= 0 ? "text-green-600" : "text-red-600"
          }`}
        >
          {item.change >= 0 ? "+" : ""}
          {formatNumber(item.change)}
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            item.changePercent >= 0
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {item.changePercent >= 0 ? "+" : ""}
          {formatNumber(item.changePercent)}%
        </span>
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900">
        {formatVolume(item.volume)}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900">
        ₹{formatNumber(item.high)}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900">
        ₹{formatNumber(item.low)}
      </td>
      <td className="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900">
        ₹{formatNumber(item.open)}
      </td>
    </tr>
  ));

  TableRow.displayName = "TableRow";

  // Memoize the rendered rows for better performance
  const renderedRows = useMemo(() => {
    return data.map((item) => <TableRow key={item.securityId} item={item} />);
  }, [data]);

  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-lg font-medium">No market data available</div>
        <div className="text-sm">
          Waiting for real-time updates from the market feed...
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <SortableHeader field="ticker">Symbol</SortableHeader>
            <SortableHeader field="exchange">Exchange</SortableHeader>
            <SortableHeader field="ltp" className="text-right">
              LTP
            </SortableHeader>
            <SortableHeader field="change" className="text-right">
              Change
            </SortableHeader>
            <SortableHeader field="changePercent" className="text-right">
              Change %
            </SortableHeader>
            <SortableHeader field="volume" className="text-right">
              Volume
            </SortableHeader>
            <SortableHeader field="high" className="text-right">
              High
            </SortableHeader>
            <SortableHeader field="low" className="text-right">
              Low
            </SortableHeader>
            <SortableHeader field="open" className="text-right">
              Open
            </SortableHeader>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {renderedRows}
        </tbody>
      </table>
    </div>
  );
};

export default memo(EnhancedMarketDataTable);
