"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface SectorData {
  sector_name: string;
  company_count: number;
}

interface StockData {
  ticker: string;
  securityId: string;
  ltp: number;
  change: number;
  changePercent: number;
  companyName?: string;
  sector?: string;
}

export default function SimpleSectors() {
  const [sectors, setSectors] = useState<SectorData[]>([]);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);
  const [sectorStocks, setSectorStocks] = useState<StockData[]>([]);
  const [loading, setLoading] = useState(true);
  const [stocksLoading, setStocksLoading] = useState(false);

  // Fetch sectors
  useEffect(() => {
    const fetchSectors = async () => {
      try {
        const response = await fetch(
          "/api/companies?action=sector-distribution"
        );
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Convert the distribution data to the expected format
            const sectorData = data.data.map((item: any) => ({
              sector_name: item.sector,
              company_count: item.count,
            }));
            setSectors(sectorData);
          }
        }
      } catch (error) {
        console.error("Error fetching sectors:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSectors();
  }, []);

  // Fetch stocks for selected sector
  const fetchSectorStocks = async (sectorName: string) => {
    setStocksLoading(true);
    try {
      // Get companies in this sector
      const companiesRes = await fetch(
        `/api/companies?action=by-sector&sector=${encodeURIComponent(sectorName)}`
      );

      if (companiesRes.ok) {
        const companiesData = await companiesRes.json();
        if (companiesData.success) {
          // Get market data
          const marketRes = await fetch("/api/data?exchange=NSE_EQ&limit=5000");
          if (marketRes.ok) {
            const marketData = await marketRes.json();
            if (marketData.latestData) {
              // Create company map
              const companyMap: { [key: string]: any } = {};
              companiesData.data.forEach((company: any) => {
                // Map by security IDs (this is how websocket data matches)
                if (company.nse_security_id) {
                  companyMap[company.nse_security_id] = company;
                }
                if (company.bse_security_id) {
                  companyMap[company.bse_security_id] = company;
                }
                // Also map by symbol as fallback
                if (company.symbol) {
                  companyMap[company.symbol] = company;
                }
              });

              // Filter market data for this sector
              const sectorStocksData = marketData.latestData
                .filter(
                  (stock: any) =>
                    stock &&
                    stock.ticker &&
                    (companyMap[stock.securityId] || companyMap[stock.ticker])
                )
                .map((stock: any) => {
                  const company =
                    companyMap[stock.securityId] || companyMap[stock.ticker];
                  return {
                    ticker: stock.ticker,
                    securityId: stock.securityId,
                    ltp: stock.ltp || 0,
                    change: stock.change || 0,
                    changePercent: stock.changePercent || 0,
                    companyName: company.company_name || stock.ticker,
                    sector: company.sector_name,
                  };
                })
                .sort(
                  (a: any, b: any) =>
                    Math.abs(b.changePercent) - Math.abs(a.changePercent)
                )
                .slice(0, 20); // Top 20 by change

              setSectorStocks(sectorStocksData);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error fetching sector stocks:", error);
    } finally {
      setStocksLoading(false);
    }
  };

  const handleSectorClick = (sectorName: string) => {
    setSelectedSector(sectorName);
    fetchSectorStocks(sectorName);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-xl">Loading sectors...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Sectors Dashboard</h1>
        <Link href="/simple-dashboard">
          <Button variant="outline">Back to Dashboard</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sectors List */}
        <Card>
          <CardHeader>
            <CardTitle>Sectors ({sectors.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {sectors.map((sector) => (
                <div
                  key={sector.sector_name}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedSector === sector.sector_name
                      ? "bg-blue-50 border-blue-300"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleSectorClick(sector.sector_name)}
                >
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{sector.sector_name}</span>
                    <span className="text-sm text-gray-600">
                      {sector.company_count} companies
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sector Stocks */}
        <Card>
          <CardHeader>
            <CardTitle>
              {selectedSector ? `${selectedSector} Stocks` : "Select a Sector"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stocksLoading ? (
              <div className="text-center py-8">Loading stocks...</div>
            ) : selectedSector ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {sectorStocks.map((stock) => (
                  <div key={stock.securityId} className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-semibold">{stock.ticker}</div>
                        <div className="text-sm text-gray-600">
                          {stock.companyName}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">
                          ₹{stock.ltp.toFixed(2)}
                        </div>
                        <div
                          className={`text-sm ${stock.change >= 0 ? "text-green-600" : "text-red-600"}`}
                        >
                          {stock.change >= 0 ? "+" : ""}
                          {stock.changePercent.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {sectorStocks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No live data available for this sector
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Click on a sector to view its stocks
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
