import React, { useMemo, useState } from "react";
import <PERSON> from "next/link";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface DetailedStocksViewProps {
  data: MarketData[];
}

interface StockListProps {
  title: string;
  stocks: MarketData[];
  filterType: string;
  onFilterChange: (value: string) => void;
}

const StockList: React.FC<StockListProps> = ({
  title,
  stocks,
  filterType,
  onFilterChange,
}) => {
  const formatPrice = (price: number) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(2)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(2)}L`;
    } else if (price >= 1000) {
      return `₹${(price / 1000).toFixed(2)}K`;
    }
    return `₹${price.toFixed(2)}`;
  };

  const getStockAvatar = (ticker: string) => {
    // Get first 1-2 letters of ticker for avatar
    const initials =
      ticker.length >= 2 ? ticker.substring(0, 2) : ticker.substring(0, 1);

    // Generate consistent colors based on ticker
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-red-500",
      "bg-yellow-500",
      "bg-indigo-500",
      "bg-pink-500",
      "bg-orange-500",
    ];

    let hash = 0;
    for (let i = 0; i < ticker.length; i++) {
      hash = ticker.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      initials: initials.toUpperCase(),
      color: colors[colorIndex],
    };
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
        <div className="relative">
          <select
            value={filterType}
            onChange={(e) => onFilterChange(e.target.value)}
            className="bg-gray-800 text-white px-3 py-1 rounded border border-gray-700 text-sm focus:outline-none focus:border-blue-500"
          >
            <option value="all">All Stocks</option>
            <option value="NSE_EQ">NSE Equity</option>
            <option value="BSE_EQ">BSE Equity</option>
            <option value="NSE_FNO">NSE F&O</option>
          </select>
        </div>
      </div>

      {/* Column Headers */}
      <div className="grid grid-cols-4 gap-4 text-xs text-gray-400 mb-4 px-2">
        <div>Name</div>
        <div className="text-right">Price</div>
        <div className="text-right">24h Change</div>
        <div></div>
      </div>

      {/* Stock List */}
      <div className="space-y-3">
        {stocks.slice(0, 10).map((stock) => (
          <div
            key={stock.securityId}
            className="flex items-center justify-between hover:bg-gray-800 p-2 rounded transition-colors"
          >
            <div className="flex items-center space-x-3 flex-1">
              <div className="flex items-center space-x-2">
                {(() => {
                  const avatar = getStockAvatar(stock.ticker);
                  return (
                    <div
                      className={`w-8 h-8 ${avatar.color} rounded-full flex items-center justify-center text-white text-xs font-bold`}
                    >
                      {avatar.initials}
                    </div>
                  );
                })()}
                <div>
                  <Link
                    href={`/stock/${stock.ticker}`}
                    className="text-white font-medium hover:text-blue-400 transition-colors"
                  >
                    {stock.ticker}
                  </Link>
                  <div className="text-xs text-gray-400">{stock.exchange}</div>
                </div>
              </div>
            </div>

            <div className="text-right flex-1">
              <div className="text-white font-medium">
                {formatPrice(stock.ltp)}
              </div>
            </div>

            <div className="text-right flex-1">
              <div
                className={`font-medium ${
                  stock.changePercent >= 0 ? "text-green-400" : "text-red-400"
                }`}
              >
                {stock.changePercent >= 0 ? "+" : ""}
                {stock.changePercent.toFixed(2)}%
              </div>
            </div>

            <div className="w-8"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

const PriceChangeDistribution: React.FC<{ data: MarketData[] }> = ({
  data,
}) => {
  const distribution = useMemo(() => {
    const ranges = [
      { label: ">10%", min: 10, max: Infinity },
      { label: "7-10%", min: 7, max: 10 },
      { label: "5-7%", min: 5, max: 7 },
      { label: "3-5%", min: 3, max: 5 },
      { label: "0-3%", min: 0, max: 3 },
      { label: "0%", min: -0.1, max: 0.1 },
      { label: "0-3%", min: -3, max: 0 },
      { label: "3-5%", min: -5, max: -3 },
      { label: "5-7%", min: -7, max: -5 },
      { label: "7-10%", min: -10, max: -7 },
      { label: ">10%", min: -Infinity, max: -10 },
    ];

    const counts = ranges.map((range) => {
      const count = data.filter(
        (stock) =>
          stock.changePercent > range.min && stock.changePercent <= range.max
      ).length;
      return { ...range, count };
    });

    const maxCount = Math.max(...counts.map((c) => c.count));
    return counts.map((item) => ({
      ...item,
      percentage: maxCount > 0 ? (item.count / maxCount) * 100 : 0,
    }));
  }, [data]);

  const totalUp = data.filter((stock) => stock.changePercent > 0.1).length;
  const totalDown = data.filter((stock) => stock.changePercent < -0.1).length;

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <h3 className="text-lg font-semibold text-white mb-8">
        Price Change Distribution
      </h3>

      {/* Chart Area */}
      <div className="relative h-64 mb-8">
        <div className="flex items-end justify-center space-x-2 h-full">
          {distribution.map((item, index) => {
            const isPositive = index < 5;
            const isNeutral = index === 5;
            const barColor = isNeutral
              ? "bg-gray-400"
              : isPositive
                ? "bg-green-500"
                : "bg-red-500";

            return (
              <div key={index} className="flex flex-col items-center">
                <div
                  className={`w-10 ${barColor} transition-all duration-300 hover:opacity-80 rounded-t-md shadow-lg border-t-2 border-white`}
                  style={{
                    height: `${Math.max(item.percentage, 5)}%`,
                    minHeight: "20px",
                  }}
                  title={`${item.label}: ${item.count} stocks`}
                />
                {/* Bar base */}
                <div className="w-10 h-1 bg-gray-600 rounded-b-sm"></div>
              </div>
            );
          })}
        </div>

        {/* Grid lines for better visualization */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="h-full flex flex-col justify-between">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="border-t border-gray-700 opacity-30"
              ></div>
            ))}
          </div>
        </div>
      </div>

      {/* Labels */}
      <div className="flex justify-center space-x-2 mb-6">
        {distribution.map((item, index) => (
          <div
            key={index}
            className="flex flex-col items-center text-center w-10"
          >
            <div className="text-lg text-white font-bold mb-1 bg-gray-800 rounded px-2 py-1">
              {item.count}
            </div>
            <div className="text-xs text-gray-400 font-medium">
              {item.label}
            </div>
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="flex justify-between items-center text-sm border-t border-gray-700 pt-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-green-400 font-medium">
            Price up: {totalUp}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <span className="text-red-400 font-medium">
            Price down: {totalDown}
          </span>
        </div>
      </div>
    </div>
  );
};

const DetailedStocksView: React.FC<DetailedStocksViewProps> = ({ data }) => {
  const [filters, setFilters] = useState({
    hotStocks: "all",
    topGainers: "all",
    topLosers: "all",
    topVolume: "all",
  });

  const categorizedStocks = useMemo(() => {
    const filterData = (stocks: MarketData[], filterType: string) => {
      if (filterType === "all") return stocks;
      return stocks.filter((stock) => stock.exchange === filterType);
    };

    // Filter out stocks that might have invalid security IDs
    const filteredData = data.filter((stock) => {
      // Basic validation - exclude stocks with obviously invalid tickers
      return (
        stock.ticker &&
        stock.ticker !== "-" &&
        !stock.ticker.includes("-") &&
        stock.ltp > 0
      );
    });

    const sortedData = [...filteredData];

    const hotStocks = filterData(
      sortedData.sort((a, b) => b.volume - a.volume),
      filters.hotStocks
    );

    const topGainers = filterData(
      sortedData
        .filter((stock) => stock.changePercent > 0)
        .sort((a, b) => b.changePercent - a.changePercent),
      filters.topGainers
    );

    const topLosers = filterData(
      sortedData
        .filter((stock) => stock.changePercent < 0)
        .sort((a, b) => a.changePercent - b.changePercent),
      filters.topLosers
    );

    const topVolume = filterData(
      sortedData.sort((a, b) => b.volume - a.volume),
      filters.topVolume
    );

    return { hotStocks, topGainers, topLosers, topVolume };
  }, [data, filters]);

  const updateFilter = (category: string, value: string) => {
    setFilters((prev) => ({ ...prev, [category]: value }));
  };

  return (
    <div className="min-h-screen bg-black p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Market Overview
          </h1>
          <p className="text-gray-400">
            Comprehensive stock market analysis and rankings
          </p>
        </div>

        {/* Top Row - 3 Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <StockList
            title="Hot Coins"
            stocks={categorizedStocks.hotStocks}
            filterType={filters.hotStocks}
            onFilterChange={(value) => updateFilter("hotStocks", value)}
          />
          <StockList
            title="Top Gainers"
            stocks={categorizedStocks.topGainers}
            filterType={filters.topGainers}
            onFilterChange={(value) => updateFilter("topGainers", value)}
          />
          <StockList
            title="Top Losers"
            stocks={categorizedStocks.topLosers}
            filterType={filters.topLosers}
            onFilterChange={(value) => updateFilter("topLosers", value)}
          />
        </div>

        {/* Bottom Row - 2 Columns */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <StockList
            title="Top Volume"
            stocks={categorizedStocks.topVolume}
            filterType={filters.topVolume}
            onFilterChange={(value) => updateFilter("topVolume", value)}
          />
          <PriceChangeDistribution data={data} />
        </div>
      </div>
    </div>
  );
};

export default DetailedStocksView;
