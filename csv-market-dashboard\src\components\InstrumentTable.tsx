"use client";

import React, { useState, useMemo } from 'react';
import { Instrument, MarketData, InstrumentTableProps } from '@/types';

const InstrumentTable: React.FC<InstrumentTableProps> = ({
  instruments,
  marketData,
  onInstrumentSelect,
  loading = false,
}) => {
  const [sortField, setSortField] = useState<keyof Instrument>('symbol');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Sort instruments
  const sortedInstruments = useMemo(() => {
    return [...instruments].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (aValue === bValue) return 0;
      
      const comparison = aValue < bValue ? -1 : 1;
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [instruments, sortField, sortDirection]);

  const handleSort = (field: keyof Instrument) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const formatPrice = (price: number | undefined): string => {
    if (price === undefined || price === 0) return '-';
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (change: number | undefined, changePercent: number | undefined): string => {
    if (change === undefined || changePercent === undefined) return '-';
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const getChangeColor = (change: number | undefined): string => {
    if (change === undefined || change === 0) return 'text-gray-600';
    return change > 0 ? 'text-green-600' : 'text-red-600';
  };

  const formatVolume = (volume: number | undefined): string => {
    if (volume === undefined || volume === 0) return '-';
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const SortIcon = ({ field }: { field: keyof Instrument }) => {
    if (sortField !== field) {
      return <span className="text-gray-400">↕</span>;
    }
    return sortDirection === 'asc' ? <span className="text-blue-600">↑</span> : <span className="text-blue-600">↓</span>;
  };

  if (loading) {
    return (
      <div className="glass rounded-2xl shadow-lg p-6">
        <div className="flex items-center justify-center h-64">
          <div className="spinner w-8 h-8 mr-3"></div>
          <span className="text-gray-600">Loading instruments...</span>
        </div>
      </div>
    );
  }

  if (instruments.length === 0) {
    return (
      <div className="glass rounded-2xl shadow-lg p-6">
        <div className="text-center py-12">
          <p className="text-gray-600 text-lg">No instruments found</p>
          <p className="text-gray-500 text-sm mt-2">Try adjusting your filters</p>
        </div>
      </div>
    );
  }

  return (
    <div className="glass rounded-2xl shadow-lg overflow-hidden">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Market Instruments</h2>
        <p className="text-gray-600 text-sm mt-1">
          Showing {sortedInstruments.length} instruments
        </p>
      </div>

      <div className="overflow-x-auto custom-scrollbar" style={{ maxHeight: '600px' }}>
        <table className="market-table">
          <thead className="sticky top-0 bg-gray-50 z-10">
            <tr>
              <th 
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('symbol')}
              >
                <div className="flex items-center justify-between">
                  Symbol
                  <SortIcon field="symbol" />
                </div>
              </th>
              <th 
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('displayName')}
              >
                <div className="flex items-center justify-between">
                  Name
                  <SortIcon field="displayName" />
                </div>
              </th>
              <th 
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('exchange')}
              >
                <div className="flex items-center justify-between">
                  Exchange
                  <SortIcon field="exchange" />
                </div>
              </th>
              <th 
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('instrumentType')}
              >
                <div className="flex items-center justify-between">
                  Type
                  <SortIcon field="instrumentType" />
                </div>
              </th>
              <th className="text-right">LTP</th>
              <th className="text-right">Change</th>
              <th className="text-right">Volume</th>
              <th 
                className="cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('lotSize')}
              >
                <div className="flex items-center justify-between">
                  Lot Size
                  <SortIcon field="lotSize" />
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedInstruments.map((instrument) => {
              const data = marketData.get(instrument.securityId);
              return (
                <tr
                  key={instrument.securityId}
                  className="hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => onInstrumentSelect?.(instrument)}
                >
                  <td className="font-medium text-blue-600">
                    {instrument.symbol}
                  </td>
                  <td className="max-w-xs truncate" title={instrument.displayName}>
                    {instrument.displayName}
                  </td>
                  <td>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {instrument.exchange}
                    </span>
                  </td>
                  <td>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {instrument.instrumentType}
                    </span>
                  </td>
                  <td className="text-right font-medium">
                    {formatPrice(data?.ltp)}
                  </td>
                  <td className={`text-right font-medium ${getChangeColor(data?.change)}`}>
                    {formatChange(data?.change, data?.changePercent)}
                  </td>
                  <td className="text-right">
                    {formatVolume(data?.volume)}
                  </td>
                  <td className="text-right">
                    {instrument.lotSize.toLocaleString()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {sortedInstruments.length > 100 && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <p className="text-sm text-gray-600 text-center">
            Showing first 100 instruments. Use filters to narrow down results.
          </p>
        </div>
      )}
    </div>
  );
};

export default InstrumentTable;
