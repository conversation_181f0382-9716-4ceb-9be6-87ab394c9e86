"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.websocketError = exports.databaseError = exports.rateLimitError = exports.notFoundError = exports.forbiddenError = exports.authError = exports.validationError = exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.APIError = void 0;
const zod_1 = require("zod");
const config_1 = require("../config");
// Custom error class for API errors
class APIError extends Error {
    constructor(statusCode, message, code, details) {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        this.name = "APIError";
    }
}
exports.APIError = APIError;
// Error handler middleware
const errorHandler = (err, req, res, next) => {
    console.error("Error:", {
        name: err.name,
        message: err.message,
        stack: config_1.appConfig.logging.level === "debug" ? err.stack : undefined,
    });
    // Handle Zod validation errors
    if (err instanceof zod_1.ZodError) {
        return res.status(400).json({
            success: false,
            error: "Validation Error",
            details: err.errors,
        });
    }
    // Handle API errors
    if (err instanceof APIError) {
        return res.status(err.statusCode).json({
            success: false,
            error: err.message,
            code: err.code,
            details: err.details,
        });
    }
    // Handle database errors
    if (err.name === "PostgresError") {
        return res.status(500).json({
            success: false,
            error: "Database Error",
            message: "An error occurred while accessing the database",
        });
    }
    // Handle WebSocket errors
    if (err.name === "WebSocketError") {
        return res.status(503).json({
            success: false,
            error: "WebSocket Error",
            message: "Failed to establish WebSocket connection",
        });
    }
    // Handle file upload errors
    if (err.name === "MulterError") {
        return res.status(400).json({
            success: false,
            error: "File Upload Error",
            message: err.message,
        });
    }
    // Default error handler
    return res.status(500).json({
        success: false,
        error: "Internal Server Error",
        message: config_1.appConfig.logging.level === "debug"
            ? err.message
            : "An unexpected error occurred",
    });
};
exports.errorHandler = errorHandler;
// Not found handler middleware
const notFoundHandler = (req, res) => {
    res.status(404).json({
        success: false,
        error: "Not Found",
        message: `The requested resource ${req.method} ${req.originalUrl} was not found`,
    });
};
exports.notFoundHandler = notFoundHandler;
// Async handler wrapper to catch async errors
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
// Validation error handler
const validationError = (message, details) => {
    return new APIError(400, message, "VALIDATION_ERROR", details);
};
exports.validationError = validationError;
// Authentication error handler
const authError = (message = "Authentication failed") => {
    return new APIError(401, message, "AUTH_ERROR");
};
exports.authError = authError;
// Authorization error handler
const forbiddenError = (message = "Access denied") => {
    return new APIError(403, message, "FORBIDDEN");
};
exports.forbiddenError = forbiddenError;
// Resource not found error handler
const notFoundError = (message = "Resource not found") => {
    return new APIError(404, message, "NOT_FOUND");
};
exports.notFoundError = notFoundError;
// Rate limit error handler
const rateLimitError = (message = "Too many requests") => {
    return new APIError(429, message, "RATE_LIMIT_EXCEEDED");
};
exports.rateLimitError = rateLimitError;
// Database error handler
const databaseError = (message = "Database operation failed") => {
    return new APIError(500, message, "DATABASE_ERROR");
};
exports.databaseError = databaseError;
// WebSocket error handler
const websocketError = (message = "WebSocket operation failed") => {
    return new APIError(503, message, "WEBSOCKET_ERROR");
};
exports.websocketError = websocketError;
//# sourceMappingURL=error.js.map