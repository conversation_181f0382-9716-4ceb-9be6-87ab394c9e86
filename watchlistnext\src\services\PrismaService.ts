import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

class PrismaService {
  private static instance: PrismaService;
  private prisma: PrismaClient;

  private constructor() {
    this.prisma = new PrismaClient({
      log: ["error", "warn"],
    });
  }

  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  public getClient(): PrismaClient {
    return this.prisma;
  }

  // Company operations
  async findCompanyBySymbol(symbol: string) {
    try {
      const company = await this.prisma.companyList.findFirst({
        where: {
          symbol: {
            equals: symbol,
            mode: "insensitive",
          },
        },
      });

      if (company) {
        logger.info("Found company by symbol", {
          symbol,
          company: company.company_name,
          nseId: company.nse_security_id,
          bseId: company.bse_security_id,
        });

        return {
          found: true,
          ticker: symbol,
          companyName: company.company_name,
          symbol: company.symbol,
          nseId: company.nse_security_id,
          bseId: company.bse_security_id,
          sector: company.sector_name,
          industry: company.industry_new_name,
          subSector: company.sub_sector,
          microCategory: company.micro_category,
          isin: company.isin_no,
        };
      }

      logger.warn("Company not found by symbol", { symbol });
      return { found: false, ticker: symbol };
    } catch (error) {
      logger.error("Error finding company by symbol", { symbol, error });
      return { found: false, ticker: symbol, error: (error as Error).message };
    }
  }

  async getAllCompanies(limit?: number) {
    try {
      const companies = await this.prisma.companyList.findMany({
        ...(limit && { take: limit }),
        orderBy: {
          company_name: "asc",
        },
      });

      return companies.map((company) => ({
        symbol: company.symbol,
        companyName: company.company_name,
        nseId: company.nse_security_id,
        bseId: company.bse_security_id,
        sector: company.sector_name,
        industry: company.industry_new_name,
        subSector: company.sub_sector,
        microCategory: company.micro_category,
        isin: company.isin_no,
      }));
    } catch (error) {
      logger.error("Error getting all companies", { error });
      return [];
    }
  }

  async getCompaniesBySector(sector: string) {
    try {
      const companies = await this.prisma.companyList.findMany({
        where: {
          sector_name: {
            equals: sector,
            mode: "insensitive",
          },
        },
        orderBy: {
          company_name: "asc",
        },
      });

      return companies.map((company) => ({
        symbol: company.symbol,
        companyName: company.company_name,
        nseId: company.nse_security_id,
        bseId: company.bse_security_id,
        sector: company.sector_name,
        industry: company.industry_new_name,
        subSector: company.sub_sector,
        microCategory: company.micro_category,
      }));
    } catch (error) {
      logger.error("Error getting companies by sector", { sector, error });
      return [];
    }
  }

  // Market data operations
  async updateMarketData(securityId: string, exchange: string, data: any) {
    try {
      const marketData = await this.prisma.marketData.upsert({
        where: {
          security_id_exchange: {
            security_id: securityId,
            exchange: exchange,
          },
        },
        update: {
          ltp: data.ltp || 0,
          change: data.change || 0,
          change_percent: data.changePercent || 0,
          volume: BigInt(data.volume || 0),
          high: data.high || 0,
          low: data.low || 0,
          open: data.open || 0,
          close: data.close || 0,
          last_updated: new Date(),
        },
        create: {
          security_id: securityId,
          exchange: exchange,
          symbol: data.symbol || "",
          ltp: data.ltp || 0,
          change: data.change || 0,
          change_percent: data.changePercent || 0,
          volume: BigInt(data.volume || 0),
          high: data.high || 0,
          low: data.low || 0,
          open: data.open || 0,
          close: data.close || 0,
        },
      });

      return marketData;
    } catch (error) {
      logger.error("Error updating market data", {
        securityId,
        exchange,
        error,
      });
      return null;
    }
  }

  async getMarketData(securityId: string, exchange: string) {
    try {
      const marketData = await this.prisma.marketData.findUnique({
        where: {
          security_id_exchange: {
            security_id: securityId,
            exchange: exchange,
          },
        },
      });

      return marketData;
    } catch (error) {
      logger.error("Error getting market data", {
        securityId,
        exchange,
        error,
      });
      return null;
    }
  }

  async getLatestMarketDataForSymbol(symbol: string) {
    try {
      // First find the company to get security IDs
      const company = await this.findCompanyBySymbol(symbol);
      if (!company.found) {
        return null;
      }

      const marketDataPromises = [];

      if (company.nseId) {
        marketDataPromises.push(this.getMarketData(company.nseId, "NSE"));
      }

      if (company.bseId) {
        marketDataPromises.push(this.getMarketData(company.bseId, "BSE"));
      }

      const results = await Promise.all(marketDataPromises);
      const validResults = results.filter((result) => result !== null);

      // Return the most recent data
      if (validResults.length > 0) {
        return validResults.sort(
          (a, b) =>
            new Date(b.last_updated).getTime() -
            new Date(a.last_updated).getTime()
        )[0];
      }

      return null;
    } catch (error) {
      logger.error("Error getting latest market data for symbol", {
        symbol,
        error,
      });
      return null;
    }
  }

  // Watchlist operations
  async createWatchlist(name: string, description?: string) {
    try {
      const watchlist = await this.prisma.watchlist.create({
        data: {
          name,
          ...(description && { description }),
        },
      });
      return watchlist;
    } catch (error) {
      logger.error("Error creating watchlist", { name, error });
      return null;
    }
  }

  async addToWatchlist(watchlistId: number, symbol: string, exchange: string) {
    try {
      const item = await this.prisma.watchlistItem.create({
        data: {
          watchlist_id: watchlistId,
          symbol,
          exchange,
        },
      });
      return item;
    } catch (error) {
      logger.error("Error adding to watchlist", {
        watchlistId,
        symbol,
        exchange,
        error,
      });
      return null;
    }
  }

  // Index data operations
  async updateIndexData(symbol: string, data: any) {
    try {
      const indexData = await this.prisma.indexData.upsert({
        where: { symbol },
        update: {
          value: data.value || 0,
          change: data.change || 0,
          change_percent: data.changePercent || 0,
          last_updated: new Date(),
        },
        create: {
          name: data.name || symbol,
          symbol,
          value: data.value || 0,
          change: data.change || 0,
          change_percent: data.changePercent || 0,
        },
      });
      return indexData;
    } catch (error) {
      logger.error("Error updating index data", { symbol, error });
      return null;
    }
  }

  async getAllIndexData() {
    try {
      const indices = await this.prisma.indexData.findMany({
        orderBy: {
          name: "asc",
        },
      });
      return indices;
    } catch (error) {
      logger.error("Error getting all index data", { error });
      return [];
    }
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

export default PrismaService;
