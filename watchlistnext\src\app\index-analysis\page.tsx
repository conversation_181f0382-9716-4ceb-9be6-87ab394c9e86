"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from "lucide-react";
import { createWebSocketConnection } from "@/utils/websocket";
import indicesData from "../../data/indices-data.json";

interface IndexConstituent {
  companyName: string;
  symbol: string;
  weight: number;
  industry: string;
  series: string;
  isinCode: string;
  currentPrice?: number;
  change?: number;
  changePercent?: number;
}

interface IndexData {
  name: string;
  constituents: IndexConstituent[];
  totalWeight: number;
  indexValue: number;
  indexChange: number;
  indexChangePercent: number;
}

const IndexAnalysisPage: React.FC = () => {
  const [selectedIndex, setSelectedIndex] = useState<string>("BANKNIFTY");
  const [indexData, setIndexData] = useState<IndexData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const getIndexDisplayName = (indexKey: string): string => {
    const names: { [key: string]: string } = {
      BANKNIFTY: "Bank Nifty",
      NIFTYIT: "Nifty IT",
      NIFTYAUTO: "Nifty Auto",
      NIFTYPHARMA: "Nifty Pharma",
      NIFTYFMCG: "Nifty FMCG",
      NIFTYMETAL: "Nifty Metal",
      NIFTYREALTY: "Nifty Realty",
    };
    return names[indexKey] || indexKey;
  };

  const loadIndexData = useCallback(
    async (indexKey: string, retryCount = 3) => {
      try {
        setLoading(true);
        setError(null);
        const staticIndexData =
          indicesData[indexKey as keyof typeof indicesData];
        if (!staticIndexData) {
          throw new Error(`Index ${indexKey} not found`);
        }

        // Fetch real market data with retry logic
        for (let attempt = 0; attempt < retryCount; attempt++) {
          try {
            const marketResponse = await fetch(
              "/api/data?exchange=NSE_EQ&limit=25000",
              {
                headers: {
                  "Cache-Control": "no-cache",
                },
              }
            );

            if (!marketResponse.ok) {
              throw new Error(`HTTP error! status: ${marketResponse.status}`);
            }

            const marketData = await marketResponse.json();
            const liveData = marketData.latestData || [];

            // Create enriched constituents
            const enrichedConstituents = staticIndexData.constituents.map(
              (constituent) => {
                const liveStock = liveData.find(
                  (stock: Record<string, unknown>) =>
                    stock.ticker === constituent.symbol
                );

                return {
                  companyName: constituent.companyName,
                  symbol: constituent.symbol,
                  weight: constituent.weight,
                  industry: constituent.industry,
                  series: constituent.series,
                  isinCode: constituent.isinCode,
                  currentPrice: liveStock?.ltp || 0,
                  change: liveStock?.change || 0,
                  changePercent: liveStock?.changePercent || 0,
                };
              }
            );

            const totalWeight = enrichedConstituents.reduce(
              (sum, constituent) => sum + constituent.weight,
              0
            );
            const normalizedConstituents = enrichedConstituents.map(
              (constituent) => ({
                ...constituent,
                weight: (constituent.weight / totalWeight) * 100,
              })
            );

            setIndexData({
              name: getIndexDisplayName(indexKey),
              indexValue: staticIndexData.indexValue,
              indexChange: staticIndexData.indexChange,
              indexChangePercent: staticIndexData.indexChangePercent,
              totalWeight: 100,
              constituents: normalizedConstituents,
            });

            // Break the retry loop if successful
            break;
          } catch (error) {
            if (attempt === retryCount - 1) {
              throw error;
            }
            // Wait before retrying (exponential backoff)
            await new Promise((resolve) =>
              setTimeout(resolve, Math.pow(2, attempt) * 1000)
            );
          }
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(`Failed to load index data: ${errorMessage}`);
        console.error("Error loading index data:", error);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    loadIndexData(selectedIndex);

    const socketInstance = createWebSocketConnection({
      onConnect: () => {
        setIsConnected(true);
        setError(null);
      },
      onDisconnect: (reason) => {
        setIsConnected(false);
        setError(`Disconnected: ${reason}`);
      },
      onError: (error) => {
        setError(`Connection error: ${error.message}`);
      },
      onReconnect: () => {
        setIsConnected(true);
        setError(null);
        loadIndexData(selectedIndex);
      },
      onMarketData: (data: Record<string, unknown>) => {
        setIndexData((prevIndex) => {
          if (!prevIndex) return prevIndex;

          const updatedConstituents = prevIndex.constituents.map(
            (constituent) => {
              if (
                constituent.symbol === (data.ticker as string) ||
                constituent.symbol === (data.securityId as string)
              ) {
                return {
                  ...constituent,
                  currentPrice:
                    (data.ltp as number) || constituent.currentPrice,
                  change: (data.change as number) || constituent.change,
                  changePercent:
                    (data.changePercent as number) || constituent.changePercent,
                };
              }
              return constituent;
            }
          );

          return {
            ...prevIndex,
            constituents: updatedConstituents,
          };
        });
      },
    });

    return () => {
      socketInstance.disconnect();
    };
  }, [selectedIndex, loadIndexData]);

  const formatCurrency = (value: number) => {
    return `₹${value.toLocaleString("en-IN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const getTopGainers = () => {
    if (!indexData) return [];
    return indexData.constituents
      .filter((stock) => stock.changePercent && stock.changePercent > 0)
      .sort((a, b) => (b.changePercent || 0) - (a.changePercent || 0))
      .slice(0, 5);
  };

  const getTopLosers = () => {
    if (!indexData) return [];
    return indexData.constituents
      .filter((stock) => stock.changePercent && stock.changePercent < 0)
      .sort((a, b) => (a.changePercent || 0) - (b.changePercent || 0))
      .slice(0, 5);
  };

  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FFC658",
    "#FF7C7C",
    "#8DD1E1",
    "#D084D0",
  ];

  const pieChartData =
    indexData?.constituents.map((constituent, index) => ({
      name: constituent.symbol,
      value: constituent.weight,
      color: COLORS[index % COLORS.length],
      fullName: constituent.companyName,
    })) || [];

  const availableIndices = [
    { value: "BANKNIFTY", label: "Bank Nifty" },
    { value: "NIFTYIT", label: "Nifty IT" },
    { value: "NIFTYAUTO", label: "Nifty Auto" },
    { value: "NIFTYPHARMA", label: "Nifty Pharma" },
    { value: "NIFTYFMCG", label: "Nifty FMCG" },
    { value: "NIFTYMETAL", label: "Nifty Metal" },
    { value: "NIFTYREALTY", label: "Nifty Realty" },
  ];

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <p className="mt-4 text-gray-600">Loading index data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-lg w-full">
          <h2 className="text-red-700 text-lg font-semibold mb-2">
            Error Loading Data
          </h2>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => loadIndexData(selectedIndex)}
            className="mt-4 bg-red-100 text-red-700 px-4 py-2 rounded hover:bg-red-200 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Index Analysis
          </h1>

          <div className="flex items-center space-x-4 mb-6">
            <label className="text-sm font-medium text-gray-700">
              Select Index:
            </label>
            <select
              value={selectedIndex}
              onChange={(e) => setSelectedIndex(e.target.value)}
              className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Select Index"
            >
              {availableIndices.map((index) => (
                <option key={index.value} value={index.value}>
                  {index.label}
                </option>
              ))}
            </select>
          </div>

          {indexData && (
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {indexData.name}
                  </h2>
                  <p className="text-sm text-gray-600">
                    Real-time index composition and performance
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-gray-900">
                    {formatCurrency(indexData.indexValue)}
                  </div>
                  <div
                    className={`text-lg font-semibold ${
                      indexData.indexChange >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {formatChange(
                      indexData.indexChange,
                      indexData.indexChangePercent
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content Grid */}
        {indexData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Pie Chart - Index Composition */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Index Composition by Weight
              </h3>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(
                        value: number,
                        name: string,
                        props: { payload?: { fullName?: string } }
                      ) => [
                        `${value.toFixed(2)}%`,
                        props.payload?.fullName || name,
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Legend */}
              <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                {pieChartData.slice(0, 10).map((entry, index) => (
                  <div key={index} className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: entry.color }}
                    ></div>
                    <span className="truncate">
                      {entry.name} ({entry.value.toFixed(1)}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Gainers & Losers */}
            <div className="space-y-6">
              {/* Top Gainers */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <TrendingUpIcon className="w-5 h-5 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Top Gainers
                  </h3>
                </div>
                <div className="space-y-3">
                  {getTopGainers().map((stock) => (
                    <div
                      key={stock.symbol}
                      className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                    >
                      <div>
                        <div className="font-medium text-gray-900">
                          {stock.symbol}
                        </div>
                        <div className="text-sm text-gray-600">
                          Weight: {stock.weight.toFixed(2)}%
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(stock.currentPrice || 0)}
                        </div>
                        <div className="text-sm font-medium text-green-600">
                          +{stock.changePercent?.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Losers */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <TrendingDownIcon className="w-5 h-5 text-red-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Top Losers
                  </h3>
                </div>
                <div className="space-y-3">
                  {getTopLosers().map((stock) => (
                    <div
                      key={stock.symbol}
                      className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
                    >
                      <div>
                        <div className="font-medium text-gray-900">
                          {stock.symbol}
                        </div>
                        <div className="text-sm text-gray-600">
                          Weight: {stock.weight.toFixed(2)}%
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(stock.currentPrice || 0)}
                        </div>
                        <div className="text-sm font-medium text-red-600">
                          {stock.changePercent?.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Detailed Constituents Table */}
        {indexData && (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">
                Index Constituents
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Complete list of stocks in {indexData.name}
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Company
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Symbol
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Weight (%)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Change
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {indexData.constituents.map((constituent) => (
                    <tr key={constituent.symbol} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {constituent.companyName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {constituent.industry}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {constituent.symbol}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {constituent.weight.toFixed(2)}%
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(constituent.currentPrice || 0)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div
                          className={`text-sm font-medium ${
                            (constituent.changePercent || 0) >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {constituent.change && constituent.changePercent
                            ? formatChange(
                                constituent.change,
                                constituent.changePercent
                              )
                            : "N/A"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IndexAnalysisPage;
