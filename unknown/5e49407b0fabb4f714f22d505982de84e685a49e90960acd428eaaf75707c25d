{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/server/*": ["./src/server/*"], "@/services/*": ["./src/services/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["src/server/**/*", "src/services/**/*", "src/types/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/app/**/*", "src/components/**/*"]}