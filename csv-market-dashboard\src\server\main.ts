// ============================================================================
// MAIN SERVER ENTRY POINT - CSV Market Dashboard Server
// ============================================================================

import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
// import { csvService } from '@/services/CSVService';
const { SimpleCSVService } = require('@/services/SimpleCSVService');
import { marketDataService } from '@/services/MarketDataService';
import { Instrument, MarketData, InstrumentFilter } from '@/types';

class CSVMarketServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private port: number;
  private instruments: Instrument[] = [];
  private connectedClients: Set<string> = new Set();
  private csvService: any;

  constructor() {
    this.port = parseInt(process.env.PORT || '8080');
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    // Initialize CSV service
    this.csvService = new SimpleCSVService('./instruments.csv');

    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
    this.setupMarketDataHandlers();
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable for development
    }));

    // CORS
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true,
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      message: 'Too many requests from this IP',
    });
    this.app.use('/api/', limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Static files
    this.app.use(express.static('public'));
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: Date.now(),
        server: 'CSV Market Dashboard',
        version: '1.0.0',
        instruments: this.instruments.length,
        connections: this.connectedClients.size,
        marketDataConnected: marketDataService.isConnected(),
      });
    });

    // Get all instruments
    this.app.get('/api/instruments', async (req, res) => {
      try {
        const filter: InstrumentFilter = {
          exchange: req.query.exchange ? (req.query.exchange as string).split(',') : undefined,
          instrumentType: req.query.instrumentType ? (req.query.instrumentType as string).split(',') : undefined,
          segment: req.query.segment ? (req.query.segment as string).split(',') : undefined,
          search: req.query.search as string,
          isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        };

        const result = await this.csvService.getInstruments(filter);
        res.json({
          success: true,
          data: result,
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Search instruments
    this.app.get('/api/instruments/search', async (req, res) => {
      try {
        const query = req.query.q as string;
        const limit = parseInt(req.query.limit as string) || 50;

        if (!query) {
          return res.status(400).json({
            success: false,
            error: 'Search query is required',
            timestamp: Date.now(),
          });
        }

        const results = await this.csvService.searchInstruments(query, limit);
        res.json({
          success: true,
          data: results,
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Get instrument by ID
    this.app.get('/api/instruments/:id', async (req, res) => {
      try {
        const instrument = await this.csvService.getInstrumentById(req.params.id);
        if (!instrument) {
          return res.status(404).json({
            success: false,
            error: 'Instrument not found',
            timestamp: Date.now(),
          });
        }

        res.json({
          success: true,
          data: instrument,
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Get exchanges
    this.app.get('/api/exchanges', async (req, res) => {
      try {
        const exchanges = await this.csvService.getExchanges();
        res.json({
          success: true,
          data: exchanges,
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Get instrument types
    this.app.get('/api/instrument-types', async (req, res) => {
      try {
        const types = await this.csvService.getInstrumentTypes();
        res.json({
          success: true,
          data: types,
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Get market data
    this.app.get('/api/market-data', (req, res) => {
      try {
        const allMarketData = marketDataService.getAllMarketData();
        const marketDataArray = Array.from(allMarketData.values());

        res.json({
          success: true,
          data: {
            connected: marketDataService.isConnected(),
            instruments: marketDataArray,
            totalInstruments: marketDataArray.length,
            activeSubscriptions: marketDataService.getSubscriptionCount(),
            lastUpdate: Date.now(),
          },
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });

    // Get NSE derivatives and index instruments
    this.app.get('/api/nse-derivatives', async (req, res) => {
      try {
        const instruments = await csvService.getNSEDerivativesAndIndex();
        const securityIds = instruments.map(inst => inst.securityId);

        res.json({
          success: true,
          data: {
            instruments,
            securityIds,
            count: instruments.length,
            breakdown: instruments.reduce((acc, inst) => {
              acc[inst.instrumentType] = (acc[inst.instrumentType] || 0) + 1;
              return acc;
            }, {} as Record<string, number>),
          },
          timestamp: Date.now(),
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: (error as Error).message,
          timestamp: Date.now(),
        });
      }
    });
  }

  /**
   * Setup Socket.IO handlers
   */
  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      this.connectedClients.add(socket.id);
      console.log(`👤 Client connected: ${socket.id} (Total: ${this.connectedClients.size})`);

      // Send initial data
      socket.emit('initialData', {
        instruments: this.instruments.slice(0, 100), // Send first 100 instruments
        marketData: Array.from(marketDataService.getAllMarketData().values()),
        connected: marketDataService.isConnected(),
      });

      // Handle subscription requests
      socket.on('subscribe', (data: { securityIds: string[] }) => {
        try {
          const instruments = this.instruments.filter(inst => 
            data.securityIds.includes(inst.securityId)
          );
          marketDataService.subscribe(instruments);
          console.log(`📡 Client ${socket.id} subscribed to ${instruments.length} instruments`);
        } catch (error) {
          socket.emit('error', { message: 'Subscription failed', error: (error as Error).message });
        }
      });

      // Handle unsubscription requests
      socket.on('unsubscribe', (data: { securityIds: string[] }) => {
        try {
          const instruments = this.instruments.filter(inst => 
            data.securityIds.includes(inst.securityId)
          );
          marketDataService.unsubscribe(instruments);
          console.log(`📡 Client ${socket.id} unsubscribed from ${instruments.length} instruments`);
        } catch (error) {
          socket.emit('error', { message: 'Unsubscription failed', error: (error as Error).message });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.connectedClients.delete(socket.id);
        console.log(`👤 Client disconnected: ${socket.id} (Total: ${this.connectedClients.size})`);
      });
    });
  }

  /**
   * Setup market data event handlers
   */
  private setupMarketDataHandlers(): void {
    marketDataService.on('connected', () => {
      console.log('📡 Market data service connected');
      this.io.emit('connectionStatus', { connected: true, timestamp: Date.now() });
    });

    marketDataService.on('disconnected', () => {
      console.log('📡 Market data service disconnected');
      this.io.emit('connectionStatus', { connected: false, timestamp: Date.now() });
    });

    marketDataService.on('marketData', (data: MarketData) => {
      // Broadcast market data to all connected clients
      this.io.emit('marketData', data);
    });

    marketDataService.on('error', (error: Error) => {
      console.error('❌ Market data service error:', error);
      this.io.emit('error', { message: 'Market data error', error: error.message });
    });
  }

  /**
   * Start the server
   */
  async start(): Promise<void> {
    try {
      console.log('🚀 Starting CSV Market Dashboard Server...');

      // Load instruments from CSV
      console.log('📊 Loading instruments from CSV...');
      this.instruments = await this.csvService.loadInstruments();
      console.log(`✅ Loaded ${this.instruments.length} instruments`);

      // Connect to market data service
      console.log('📡 Connecting to market data service...');
      await marketDataService.connect();

      // Auto-subscribe to NSE derivatives and index instruments
      console.log('🎯 Auto-subscribing to NSE derivatives and index instruments...');
      const nseDerivatives = await this.csvService.getNSEDerivativesAndIndex();
      if (nseDerivatives.length > 0) {
        marketDataService.subscribe(nseDerivatives);
        console.log(`✅ Auto-subscribed to ${nseDerivatives.length} NSE derivatives and index instruments`);
      }

      // Start HTTP server
      await new Promise<void>((resolve, reject) => {
        this.server.listen(this.port, (error?: Error) => {
          if (error) {
            reject(error);
          } else {
            console.log(`🌐 Server running on http://localhost:${this.port}`);
            resolve();
          }
        });
      });

      console.log('🎉 CSV Market Dashboard Server started successfully!');

    } catch (error) {
      console.error('❌ Failed to start server:', error);
      throw error;
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down server...');
    
    marketDataService.disconnect();
    
    await new Promise<void>((resolve) => {
      this.server.close(() => {
        console.log('✅ Server shutdown complete');
        resolve();
      });
    });
  }
}

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown handlers
let server: CSVMarketServer | null = null;

const gracefulShutdown = async (signal: string) => {
  console.log(`📡 Received ${signal}, starting graceful shutdown...`);
  
  if (server) {
    try {
      await server.shutdown();
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  } else {
    process.exit(0);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the server
async function main() {
  try {
    server = new CSVMarketServer();
    await server.start();
  } catch (error) {
    console.error('❌ Failed to start application:', error);
    process.exit(1);
  }
}

// Run the application
if (require.main === module) {
  main().catch(console.error);
}
