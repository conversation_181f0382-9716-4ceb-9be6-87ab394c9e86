import React, { useMemo } from "react";
import Link from "next/link";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface StocksColumnLayoutProps {
  data: MarketData[];
}

interface StockColumnProps {
  title: string;
  stocks: MarketData[];
  icon: string;
}

const StockColumn: React.FC<StockColumnProps> = ({ title, stocks, icon }) => {
  const formatPrice = (price: number) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(2)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(2)}L`;
    } else if (price >= 1000) {
      return `₹${(price / 1000).toFixed(2)}K`;
    }
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    if (typeof change !== "number" || typeof changePercent !== "number") {
      return "0.00%";
    }
    const sign = change >= 0 ? "+" : "";
    return `${sign}${changePercent.toFixed(2)}%`;
  };

  const getStockAvatar = (ticker: string) => {
    // Get first 1-2 letters of ticker for avatar
    const initials =
      ticker.length >= 2 ? ticker.substring(0, 2) : ticker.substring(0, 1);

    // Generate consistent colors based on ticker
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-red-500",
      "bg-yellow-500",
      "bg-indigo-500",
      "bg-pink-500",
      "bg-orange-500",
    ];

    let hash = 0;
    for (let i = 0; i < ticker.length; i++) {
      hash = ticker.charCodeAt(i) + ((hash << 5) - hash);
    }
    const colorIndex = Math.abs(hash) % colors.length;

    return {
      initials: initials.toUpperCase(),
      color: colors[colorIndex],
    };
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 hover:shadow-md transition-shadow">
      {/* Column Header */}
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center space-x-2">
          <span className="text-xl">{icon}</span>
          <h3 className="text-sm font-semibold text-gray-800">{title}</h3>
        </div>
        <Link
          href="/market-overview"
          className="text-xs text-blue-600 hover:text-blue-800 font-medium hover:underline transition-colors"
        >
          More
        </Link>
      </div>

      {/* Stock List */}
      <div className="space-y-4">
        {stocks.slice(0, 4).map((stock) => (
          <div
            key={stock.securityId}
            className="flex items-center justify-between hover:bg-gray-50 p-2 rounded-lg transition-colors"
          >
            {/* Left side - Icon, Ticker, Exchange */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {(() => {
                  const avatar = getStockAvatar(stock.ticker);
                  return (
                    <div
                      className={`w-6 h-6 ${avatar.color} rounded-full flex items-center justify-center text-white text-xs font-bold`}
                    >
                      {avatar.initials}
                    </div>
                  );
                })()}
                <div className="flex flex-col">
                  <Link
                    href={`/stock/${stock.ticker}`}
                    className="text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors"
                  >
                    {stock.ticker}
                  </Link>
                  <span className="text-xs text-gray-500 uppercase">
                    {stock.exchange}
                  </span>
                </div>
              </div>
            </div>

            {/* Right side - Price and Change */}
            <div className="text-right">
              <div className="text-sm font-bold text-gray-900">
                {formatPrice(stock.ltp)}
              </div>
              <div
                className={`text-xs font-semibold px-2 py-1 rounded-full ${
                  stock.change >= 0
                    ? "text-green-700 bg-green-100"
                    : "text-red-700 bg-red-100"
                }`}
              >
                {formatChange(stock.change, stock.changePercent)}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const StocksColumnLayout: React.FC<StocksColumnLayoutProps> = ({ data }) => {
  // Categorize stocks based on different criteria
  const categorizedStocks = useMemo(() => {
    // Filter out stocks that might have invalid security IDs
    // This is a basic filter - ideally this should be done at the data source level
    const filteredData = data.filter((stock) => {
      // Basic validation - exclude stocks with obviously invalid tickers or data
      return (
        stock.ticker &&
        stock.ticker !== "-" &&
        !stock.ticker.includes("-") &&
        stock.ltp > 0 &&
        typeof stock.change === "number" &&
        typeof stock.changePercent === "number"
      );
    });

    const sortedData = [...filteredData];

    // Hot Stocks - highest volume
    const hotStocks = sortedData
      .sort((a, b) => b.volume - a.volume)
      .slice(0, 10);

    // New Listings - recently added (using timestamp as proxy)
    const newListings = sortedData
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    // Top Gainers - highest positive percentage change
    const topGainers = sortedData
      .filter((stock) => stock.changePercent > 0)
      .sort((a, b) => b.changePercent - a.changePercent)
      .slice(0, 10);

    // Top Volume - highest trading volume
    const topVolume = sortedData
      .sort((a, b) => b.volume - a.volume)
      .slice(0, 10);

    return {
      hotStocks,
      newListings,
      topGainers,
      topVolume,
    };
  }, [data]);

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg">No stock data available</div>
        <div className="text-gray-400 text-sm mt-2">
          Waiting for real-time updates...
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StockColumn
        title="Hot Stocks"
        stocks={categorizedStocks.hotStocks}
        icon="🔥"
      />
      <StockColumn
        title="New Listing"
        stocks={categorizedStocks.newListings}
        icon="✨"
      />
      <StockColumn
        title="Top Gainer Stock"
        stocks={categorizedStocks.topGainers}
        icon="📈"
      />
      <StockColumn
        title="Top Volume Stock"
        stocks={categorizedStocks.topVolume}
        icon="📊"
      />
    </div>
  );
};

export default StocksColumnLayout;
